[{"F:\\File\\Web\\BTLNMCNNPM\\Front\\NMCNPM_IT3180_20241\\website-app\\src\\index.js": "1", "F:\\File\\Web\\BTLNMCNNPM\\Front\\NMCNPM_IT3180_20241\\website-app\\src\\App.js": "2", "F:\\File\\Web\\BTLNMCNNPM\\Front\\NMCNPM_IT3180_20241\\website-app\\src\\reportWebVitals.js": "3", "F:\\File\\Web\\BTLNMCNNPM\\Front\\NMCNPM_IT3180_20241\\website-app\\src\\components\\introduce\\Loading.js": "4", "F:\\File\\Web\\BTLNMCNNPM\\Front\\NMCNPM_IT3180_20241\\website-app\\src\\components\\introduce\\AuthContext.js": "5", "F:\\File\\Web\\BTLNMCNNPM\\Front\\NMCNPM_IT3180_20241\\website-app\\src\\components\\introduce\\Main_intro.js": "6", "F:\\File\\Web\\BTLNMCNNPM\\Front\\NMCNPM_IT3180_20241\\website-app\\src\\components\\introduce\\protect.js": "7", "F:\\File\\Web\\BTLNMCNNPM\\Front\\NMCNPM_IT3180_20241\\website-app\\src\\components\\Notification\\notification.js": "8", "F:\\File\\Web\\BTLNMCNNPM\\Front\\NMCNPM_IT3180_20241\\website-app\\src\\pages\\Calendar\\index.js": "9", "F:\\File\\Web\\BTLNMCNNPM\\Front\\NMCNPM_IT3180_20241\\website-app\\src\\pages\\Profile\\index.js": "10", "F:\\File\\Web\\BTLNMCNNPM\\Front\\NMCNPM_IT3180_20241\\website-app\\src\\pages\\Surprised\\index.js": "11", "F:\\File\\Web\\BTLNMCNNPM\\Front\\NMCNPM_IT3180_20241\\website-app\\src\\pages\\ManageAccount\\index.js": "12", "F:\\File\\Web\\BTLNMCNNPM\\Front\\NMCNPM_IT3180_20241\\website-app\\src\\pages\\Permission\\index.js": "13", "F:\\File\\Web\\BTLNMCNNPM\\Front\\NMCNPM_IT3180_20241\\website-app\\src\\pages\\RolesGroup\\index.js": "14", "F:\\File\\Web\\BTLNMCNNPM\\Front\\NMCNPM_IT3180_20241\\website-app\\src\\pages\\ManageProduct\\index.js": "15", "F:\\File\\Web\\BTLNMCNNPM\\Front\\NMCNPM_IT3180_20241\\website-app\\src\\pages\\home\\index.js": "16", "F:\\File\\Web\\BTLNMCNNPM\\Front\\NMCNPM_IT3180_20241\\website-app\\src\\components\\introduce\\intro.js": "17", "F:\\File\\Web\\BTLNMCNNPM\\Front\\NMCNPM_IT3180_20241\\website-app\\src\\pages\\Page404\\index.js": "18", "F:\\File\\Web\\BTLNMCNNPM\\Front\\NMCNPM_IT3180_20241\\website-app\\src\\pages\\Import\\index.js": "19", "F:\\File\\Web\\BTLNMCNNPM\\Front\\NMCNPM_IT3180_20241\\website-app\\src\\pages\\Export\\index.js": "20", "F:\\File\\Web\\BTLNMCNNPM\\Front\\NMCNPM_IT3180_20241\\website-app\\src\\layouts\\LayoutDefault\\index.js": "21", "F:\\File\\Web\\BTLNMCNNPM\\Front\\NMCNPM_IT3180_20241\\website-app\\src\\pages\\Profile\\image.js": "22", "F:\\File\\Web\\BTLNMCNNPM\\Front\\NMCNPM_IT3180_20241\\website-app\\src\\components\\introduce\\useAuth.js": "23", "F:\\File\\Web\\BTLNMCNNPM\\Front\\NMCNPM_IT3180_20241\\website-app\\src\\pages\\home\\sale_daily.js": "24", "F:\\File\\Web\\BTLNMCNNPM\\Front\\NMCNPM_IT3180_20241\\website-app\\src\\pages\\home\\useronlinecard.js": "25", "F:\\File\\Web\\BTLNMCNNPM\\Front\\NMCNPM_IT3180_20241\\website-app\\src\\services\\Roles\\rolesService.js": "26", "F:\\File\\Web\\BTLNMCNNPM\\Front\\NMCNPM_IT3180_20241\\website-app\\src\\components\\introduce\\forgot_password.js": "27", "F:\\File\\Web\\BTLNMCNNPM\\Front\\NMCNPM_IT3180_20241\\website-app\\src\\components\\introduce\\resetpassword.js": "28", "F:\\File\\Web\\BTLNMCNNPM\\Front\\NMCNPM_IT3180_20241\\website-app\\src\\components\\Manage_product\\index.js": "29", "F:\\File\\Web\\BTLNMCNNPM\\Front\\NMCNPM_IT3180_20241\\website-app\\src\\components\\Manage_product\\ProductForm.js": "30", "F:\\File\\Web\\BTLNMCNNPM\\Front\\NMCNPM_IT3180_20241\\website-app\\src\\pages\\Import\\ModalHistory.js": "31", "F:\\File\\Web\\BTLNMCNNPM\\Front\\NMCNPM_IT3180_20241\\website-app\\src\\pages\\Import\\ModalDetail.js": "32", "F:\\File\\Web\\BTLNMCNNPM\\Front\\NMCNPM_IT3180_20241\\website-app\\src\\components\\Avatar\\index.js": "33", "F:\\File\\Web\\BTLNMCNNPM\\Front\\NMCNPM_IT3180_20241\\website-app\\src\\pages\\home\\chat.js": "34", "F:\\File\\Web\\BTLNMCNNPM\\Front\\NMCNPM_IT3180_20241\\website-app\\src\\components\\test\\index.js": "35", "F:\\File\\Web\\BTLNMCNNPM\\Front\\NMCNPM_IT3180_20241\\website-app\\src\\components\\export\\main.js": "36", "F:\\File\\Web\\BTLNMCNNPM\\Front\\NMCNPM_IT3180_20241\\website-app\\src\\components\\Manage_product\\item.js": "37", "F:\\File\\Web\\BTLNMCNNPM\\Front\\NMCNPM_IT3180_20241\\website-app\\src\\components\\Manage_product\\history.js": "38", "F:\\File\\Web\\BTLNMCNNPM\\Front\\NMCNPM_IT3180_20241\\website-app\\src\\components\\export\\form_show.js": "39", "F:\\File\\Web\\BTLNMCNNPM\\Front\\NMCNPM_IT3180_20241\\website-app\\src\\components\\ComponentExport\\Modal\\index.js": "40", "F:\\File\\Web\\BTLNMCNNPM\\Front\\NMCNPM_IT3180_20241\\website-app\\src\\components\\Sidebar\\index.js": "41", "F:\\File\\Web\\BTLNMCNNPM\\Front\\NMCNPM_IT3180_20241\\website-app\\src\\components\\Header\\index.js": "42", "F:\\File\\Web\\BTLNMCNNPM\\Front\\NMCNPM_IT3180_20241\\website-app\\src\\components\\export\\history.js": "43", "F:\\File\\Web\\BTLNMCNNPM\\Front\\NMCNPM_IT3180_20241\\website-app\\src\\components\\export\\thanh_toan.js": "44", "F:\\File\\Web\\BTLNMCNNPM\\Front\\NMCNPM_IT3180_20241\\website-app\\src\\components\\Manage_product\\Form_delete.js": "45", "F:\\File\\Web\\BTLNMCNNPM\\Front\\NMCNPM_IT3180_20241\\website-app\\src\\components\\Header\\noti.js": "46", "F:\\File\\Web\\BTLNMCNNPM\\Front\\NMCNPM_IT3180_20241\\website-app\\src\\components\\Header\\music.js": "47", "F:\\File\\Web\\BTLNMCNNPM\\Front\\NMCNPM_IT3180_20241\\website-app\\src\\components\\Manage_product\\Product_detail.js": "48", "F:\\File\\Web\\BTLNMCNNPM\\Front\\NMCNPM_IT3180_20241\\website-app\\src\\components\\export\\formcustomer.js": "49", "F:\\File\\Web\\BTLNMCNNPM\\Front\\NMCNPM_IT3180_20241\\website-app\\src\\components\\Modal\\index.js": "50", "F:\\File\\Web\\BTLNMCNNPM\\Front\\NMCNPM_IT3180_20241\\website-app\\src\\pages\\Import\\index2.js": "51"}, {"size": 873, "mtime": 1748099557892, "results": "52", "hashOfConfig": "53"}, {"size": 2080, "mtime": 1748099557807, "results": "54", "hashOfConfig": "53"}, {"size": 375, "mtime": 1748099557918, "results": "55", "hashOfConfig": "53"}, {"size": 873, "mtime": 1748099557881, "results": "56", "hashOfConfig": "53"}, {"size": 1324, "mtime": 1748099557880, "results": "57", "hashOfConfig": "53"}, {"size": 2947, "mtime": 1748099557881, "results": "58", "hashOfConfig": "53"}, {"size": 778, "mtime": 1748099557888, "results": "59", "hashOfConfig": "53"}, {"size": 2107, "mtime": 1748099557862, "results": "60", "hashOfConfig": "53"}, {"size": 9872, "mtime": 1748502443203, "results": "61", "hashOfConfig": "53"}, {"size": 12494, "mtime": 1748511095235, "results": "62", "hashOfConfig": "53"}, {"size": 1975, "mtime": 1748099557914, "results": "63", "hashOfConfig": "53"}, {"size": 20705, "mtime": 1748517729272, "results": "64", "hashOfConfig": "53"}, {"size": 5142, "mtime": 1748506973873, "results": "65", "hashOfConfig": "53"}, {"size": 5569, "mtime": 1748099557913, "results": "66", "hashOfConfig": "53"}, {"size": 203, "mtime": 1748099557903, "results": "67", "hashOfConfig": "53"}, {"size": 40528, "mtime": 1748504662284, "results": "68", "hashOfConfig": "53"}, {"size": 13202, "mtime": 1748502443937, "results": "69", "hashOfConfig": "53"}, {"size": 550, "mtime": 1748099557908, "results": "70", "hashOfConfig": "53"}, {"size": 50900, "mtime": 1748507085743, "results": "71", "hashOfConfig": "53"}, {"size": 173, "mtime": 1748099557897, "results": "72", "hashOfConfig": "53"}, {"size": 2075, "mtime": 1748099557894, "results": "73", "hashOfConfig": "53"}, {"size": 1378, "mtime": 1748099557911, "results": "74", "hashOfConfig": "53"}, {"size": 181, "mtime": 1748099557890, "results": "75", "hashOfConfig": "53"}, {"size": 3693, "mtime": 1748504480080, "results": "76", "hashOfConfig": "53"}, {"size": 2687, "mtime": 1748504554127, "results": "77", "hashOfConfig": "53"}, {"size": 2385, "mtime": 1748517316159, "results": "78", "hashOfConfig": "53"}, {"size": 5232, "mtime": 1748502443971, "results": "79", "hashOfConfig": "53"}, {"size": 2838, "mtime": 1748502444197, "results": "80", "hashOfConfig": "53"}, {"size": 5291, "mtime": 1748099557858, "results": "81", "hashOfConfig": "53"}, {"size": 19243, "mtime": 1748502443913, "results": "82", "hashOfConfig": "53"}, {"size": 7039, "mtime": 1748502443954, "results": "83", "hashOfConfig": "53"}, {"size": 16375, "mtime": 1748502443393, "results": "84", "hashOfConfig": "53"}, {"size": 863, "mtime": 1748099557809, "results": "85", "hashOfConfig": "53"}, {"size": 7643, "mtime": 1748504280903, "results": "86", "hashOfConfig": "53"}, {"size": 13087, "mtime": 1748506879841, "results": "87", "hashOfConfig": "53"}, {"size": 18020, "mtime": 1748568907332, "results": "88", "hashOfConfig": "53"}, {"size": 7395, "mtime": 1748502443746, "results": "89", "hashOfConfig": "53"}, {"size": 5446, "mtime": 1748568379493, "results": "90", "hashOfConfig": "53"}, {"size": 14268, "mtime": 1748568488371, "results": "91", "hashOfConfig": "53"}, {"size": 616, "mtime": 1748099557814, "results": "92", "hashOfConfig": "53"}, {"size": 8921, "mtime": 1748517914216, "results": "93", "hashOfConfig": "53"}, {"size": 926, "mtime": 1748099557847, "results": "94", "hashOfConfig": "53"}, {"size": 6583, "mtime": 1748568254665, "results": "95", "hashOfConfig": "53"}, {"size": 6983, "mtime": 1748502443966, "results": "96", "hashOfConfig": "53"}, {"size": 1839, "mtime": 1748099557852, "results": "97", "hashOfConfig": "53"}, {"size": 4217, "mtime": 1748502443795, "results": "98", "hashOfConfig": "53"}, {"size": 258, "mtime": 1748099557848, "results": "99", "hashOfConfig": "53"}, {"size": 22766, "mtime": 1748502443451, "results": "100", "hashOfConfig": "53"}, {"size": 6276, "mtime": 1748568241569, "results": "101", "hashOfConfig": "53"}, {"size": 2375, "mtime": 1748099557861, "results": "102", "hashOfConfig": "53"}, {"size": 24591, "mtime": 1748505170630, "results": "103", "hashOfConfig": "53"}, {"filePath": "104", "messages": "105", "suppressedMessages": "106", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1uca01g", {"filePath": "107", "messages": "108", "suppressedMessages": "109", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "110", "messages": "111", "suppressedMessages": "112", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "113", "messages": "114", "suppressedMessages": "115", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "116", "messages": "117", "suppressedMessages": "118", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "119", "messages": "120", "suppressedMessages": "121", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "122", "messages": "123", "suppressedMessages": "124", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "125", "messages": "126", "suppressedMessages": "127", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "128", "messages": "129", "suppressedMessages": "130", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "131", "messages": "132", "suppressedMessages": "133", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 9, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "134", "messages": "135", "suppressedMessages": "136", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "137", "messages": "138", "suppressedMessages": "139", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 34, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "140", "messages": "141", "suppressedMessages": "142", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "143", "messages": "144", "suppressedMessages": "145", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "146", "messages": "147", "suppressedMessages": "148", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "149", "messages": "150", "suppressedMessages": "151", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 13, "fixableErrorCount": 0, "fixableWarningCount": 1, "source": null}, {"filePath": "152", "messages": "153", "suppressedMessages": "154", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "155", "messages": "156", "suppressedMessages": "157", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "158", "messages": "159", "suppressedMessages": "160", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 16, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "161", "messages": "162", "suppressedMessages": "163", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "164", "messages": "165", "suppressedMessages": "166", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "167", "messages": "168", "suppressedMessages": "169", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 1, "source": null}, {"filePath": "170", "messages": "171", "suppressedMessages": "172", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "173", "messages": "174", "suppressedMessages": "175", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "176", "messages": "177", "suppressedMessages": "178", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "179", "messages": "180", "suppressedMessages": "181", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "182", "messages": "183", "suppressedMessages": "184", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "185", "messages": "186", "suppressedMessages": "187", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "188", "messages": "189", "suppressedMessages": "190", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "191", "messages": "192", "suppressedMessages": "193", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "194", "messages": "195", "suppressedMessages": "196", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "197", "messages": "198", "suppressedMessages": "199", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "200", "messages": "201", "suppressedMessages": "202", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "203", "messages": "204", "suppressedMessages": "205", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "206", "messages": "207", "suppressedMessages": "208", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "209", "messages": "210", "suppressedMessages": "211", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 12, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "212", "messages": "213", "suppressedMessages": "214", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 11, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "215", "messages": "216", "suppressedMessages": "217", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "218", "messages": "219", "suppressedMessages": "220", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "221", "messages": "222", "suppressedMessages": "223", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "224", "messages": "225", "suppressedMessages": "226", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "227", "messages": "228", "suppressedMessages": "229", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "230", "messages": "231", "suppressedMessages": "232", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "233", "messages": "234", "suppressedMessages": "235", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "236", "messages": "237", "suppressedMessages": "238", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "239", "messages": "240", "suppressedMessages": "241", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "242", "messages": "243", "suppressedMessages": "244", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "245", "messages": "246", "suppressedMessages": "247", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "248", "messages": "249", "suppressedMessages": "250", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "251", "messages": "252", "suppressedMessages": "253", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "254", "messages": "255", "suppressedMessages": "256", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 20, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "F:\\File\\Web\\BTLNMCNNPM\\Front\\NMCNPM_IT3180_20241\\website-app\\src\\index.js", [], [], "F:\\File\\Web\\BTLNMCNNPM\\Front\\NMCNPM_IT3180_20241\\website-app\\src\\App.js", ["257"], [], "F:\\File\\Web\\BTLNMCNNPM\\Front\\NMCNPM_IT3180_20241\\website-app\\src\\reportWebVitals.js", [], [], "F:\\File\\Web\\BTLNMCNNPM\\Front\\NMCNPM_IT3180_20241\\website-app\\src\\components\\introduce\\Loading.js", [], [], "F:\\File\\Web\\BTLNMCNNPM\\Front\\NMCNPM_IT3180_20241\\website-app\\src\\components\\introduce\\AuthContext.js", [], [], "F:\\File\\Web\\BTLNMCNNPM\\Front\\NMCNPM_IT3180_20241\\website-app\\src\\components\\introduce\\Main_intro.js", ["258"], [], "F:\\File\\Web\\BTLNMCNNPM\\Front\\NMCNPM_IT3180_20241\\website-app\\src\\components\\introduce\\protect.js", [], [], "F:\\File\\Web\\BTLNMCNNPM\\Front\\NMCNPM_IT3180_20241\\website-app\\src\\components\\Notification\\notification.js", [], [], "F:\\File\\Web\\BTLNMCNNPM\\Front\\NMCNPM_IT3180_20241\\website-app\\src\\pages\\Calendar\\index.js", ["259", "260"], [], "F:\\File\\Web\\BTLNMCNNPM\\Front\\NMCNPM_IT3180_20241\\website-app\\src\\pages\\Profile\\index.js", ["261", "262", "263", "264", "265", "266", "267", "268", "269"], [], "F:\\File\\Web\\BTLNMCNNPM\\Front\\NMCNPM_IT3180_20241\\website-app\\src\\pages\\Surprised\\index.js", ["270"], [], "F:\\File\\Web\\BTLNMCNNPM\\Front\\NMCNPM_IT3180_20241\\website-app\\src\\pages\\ManageAccount\\index.js", ["271", "272", "273", "274", "275", "276", "277", "278", "279", "280", "281", "282", "283", "284", "285", "286", "287", "288", "289", "290", "291", "292", "293", "294", "295", "296", "297", "298", "299", "300", "301", "302", "303", "304"], [], "F:\\File\\Web\\BTLNMCNNPM\\Front\\NMCNPM_IT3180_20241\\website-app\\src\\pages\\Permission\\index.js", ["305", "306", "307", "308"], [], "F:\\File\\Web\\BTLNMCNNPM\\Front\\NMCNPM_IT3180_20241\\website-app\\src\\pages\\RolesGroup\\index.js", ["309", "310"], [], "F:\\File\\Web\\BTLNMCNNPM\\Front\\NMCNPM_IT3180_20241\\website-app\\src\\pages\\ManageProduct\\index.js", [], [], "F:\\File\\Web\\BTLNMCNNPM\\Front\\NMCNPM_IT3180_20241\\website-app\\src\\pages\\home\\index.js", ["311", "312", "313", "314", "315", "316", "317", "318", "319", "320", "321", "322", "323"], [], "F:\\File\\Web\\BTLNMCNNPM\\Front\\NMCNPM_IT3180_20241\\website-app\\src\\components\\introduce\\intro.js", ["324", "325", "326", "327", "328", "329", "330"], [], "F:\\File\\Web\\BTLNMCNNPM\\Front\\NMCNPM_IT3180_20241\\website-app\\src\\pages\\Page404\\index.js", ["331", "332"], [], "F:\\File\\Web\\BTLNMCNNPM\\Front\\NMCNPM_IT3180_20241\\website-app\\src\\pages\\Import\\index.js", ["333", "334", "335", "336", "337", "338", "339", "340", "341", "342", "343", "344", "345", "346", "347", "348"], [], "F:\\File\\Web\\BTLNMCNNPM\\Front\\NMCNPM_IT3180_20241\\website-app\\src\\pages\\Export\\index.js", [], [], "F:\\File\\Web\\BTLNMCNNPM\\Front\\NMCNPM_IT3180_20241\\website-app\\src\\layouts\\LayoutDefault\\index.js", ["349", "350"], [], "F:\\File\\Web\\BTLNMCNNPM\\Front\\NMCNPM_IT3180_20241\\website-app\\src\\pages\\Profile\\image.js", ["351", "352", "353"], [], "F:\\File\\Web\\BTLNMCNNPM\\Front\\NMCNPM_IT3180_20241\\website-app\\src\\components\\introduce\\useAuth.js", [], [], "F:\\File\\Web\\BTLNMCNNPM\\Front\\NMCNPM_IT3180_20241\\website-app\\src\\pages\\home\\sale_daily.js", ["354", "355"], [], "F:\\File\\Web\\BTLNMCNNPM\\Front\\NMCNPM_IT3180_20241\\website-app\\src\\pages\\home\\useronlinecard.js", ["356", "357", "358", "359"], [], "F:\\File\\Web\\BTLNMCNNPM\\Front\\NMCNPM_IT3180_20241\\website-app\\src\\services\\Roles\\rolesService.js", ["360"], [], "F:\\File\\Web\\BTLNMCNNPM\\Front\\NMCNPM_IT3180_20241\\website-app\\src\\components\\introduce\\forgot_password.js", [], [], "F:\\File\\Web\\BTLNMCNNPM\\Front\\NMCNPM_IT3180_20241\\website-app\\src\\components\\introduce\\resetpassword.js", ["361"], [], "F:\\File\\Web\\BTLNMCNNPM\\Front\\NMCNPM_IT3180_20241\\website-app\\src\\components\\Manage_product\\index.js", ["362"], [], "F:\\File\\Web\\BTLNMCNNPM\\Front\\NMCNPM_IT3180_20241\\website-app\\src\\components\\Manage_product\\ProductForm.js", ["363", "364"], [], "F:\\File\\Web\\BTLNMCNNPM\\Front\\NMCNPM_IT3180_20241\\website-app\\src\\pages\\Import\\ModalHistory.js", ["365", "366", "367"], [], "F:\\File\\Web\\BTLNMCNNPM\\Front\\NMCNPM_IT3180_20241\\website-app\\src\\pages\\Import\\ModalDetail.js", ["368", "369", "370"], [], "F:\\File\\Web\\BTLNMCNNPM\\Front\\NMCNPM_IT3180_20241\\website-app\\src\\components\\Avatar\\index.js", [], [], "F:\\File\\Web\\BTLNMCNNPM\\Front\\NMCNPM_IT3180_20241\\website-app\\src\\pages\\home\\chat.js", ["371", "372", "373"], [], "F:\\File\\Web\\BTLNMCNNPM\\Front\\NMCNPM_IT3180_20241\\website-app\\src\\components\\test\\index.js", ["374", "375", "376", "377", "378", "379", "380", "381"], [], "F:\\File\\Web\\BTLNMCNNPM\\Front\\NMCNPM_IT3180_20241\\website-app\\src\\components\\export\\main.js", ["382", "383", "384", "385", "386", "387", "388", "389", "390", "391", "392", "393"], [], "F:\\File\\Web\\BTLNMCNNPM\\Front\\NMCNPM_IT3180_20241\\website-app\\src\\components\\Manage_product\\item.js", ["394", "395", "396", "397", "398", "399", "400", "401", "402", "403", "404"], [], "F:\\File\\Web\\BTLNMCNNPM\\Front\\NMCNPM_IT3180_20241\\website-app\\src\\components\\Manage_product\\history.js", ["405", "406"], [], "F:\\File\\Web\\BTLNMCNNPM\\Front\\NMCNPM_IT3180_20241\\website-app\\src\\components\\export\\form_show.js", ["407", "408", "409", "410", "411", "412", "413"], [], "F:\\File\\Web\\BTLNMCNNPM\\Front\\NMCNPM_IT3180_20241\\website-app\\src\\components\\ComponentExport\\Modal\\index.js", [], [], "F:\\File\\Web\\BTLNMCNNPM\\Front\\NMCNPM_IT3180_20241\\website-app\\src\\components\\Sidebar\\index.js", ["414", "415"], [], "F:\\File\\Web\\BTLNMCNNPM\\Front\\NMCNPM_IT3180_20241\\website-app\\src\\components\\Header\\index.js", ["416", "417", "418"], [], "F:\\File\\Web\\BTLNMCNNPM\\Front\\NMCNPM_IT3180_20241\\website-app\\src\\components\\export\\history.js", ["419", "420"], [], "F:\\File\\Web\\BTLNMCNNPM\\Front\\NMCNPM_IT3180_20241\\website-app\\src\\components\\export\\thanh_toan.js", ["421", "422"], [], "F:\\File\\Web\\BTLNMCNNPM\\Front\\NMCNPM_IT3180_20241\\website-app\\src\\components\\Manage_product\\Form_delete.js", [], [], "F:\\File\\Web\\BTLNMCNNPM\\Front\\NMCNPM_IT3180_20241\\website-app\\src\\components\\Header\\noti.js", ["423"], [], "F:\\File\\Web\\BTLNMCNNPM\\Front\\NMCNPM_IT3180_20241\\website-app\\src\\components\\Header\\music.js", [], [], "F:\\File\\Web\\BTLNMCNNPM\\Front\\NMCNPM_IT3180_20241\\website-app\\src\\components\\Manage_product\\Product_detail.js", ["424", "425", "426", "427", "428", "429", "430"], [], "F:\\File\\Web\\BTLNMCNNPM\\Front\\NMCNPM_IT3180_20241\\website-app\\src\\components\\export\\formcustomer.js", ["431", "432", "433", "434"], [], "F:\\File\\Web\\BTLNMCNNPM\\Front\\NMCNPM_IT3180_20241\\website-app\\src\\components\\Modal\\index.js", ["435"], [], "F:\\File\\Web\\BTLNMCNNPM\\Front\\NMCNPM_IT3180_20241\\website-app\\src\\pages\\Import\\index2.js", ["436", "437", "438", "439", "440", "441", "442", "443", "444", "445", "446", "447", "448", "449", "450", "451", "452", "453", "454", "455"], [], {"ruleId": "456", "severity": 1, "message": "457", "line": 13, "column": 8, "nodeType": "458", "messageId": "459", "endLine": 13, "endColumn": 15}, {"ruleId": "460", "severity": 1, "message": "461", "line": 22, "column": 6, "nodeType": "462", "endLine": 22, "endColumn": 8, "suggestions": "463"}, {"ruleId": "456", "severity": 1, "message": "464", "line": 23, "column": 17, "nodeType": "458", "messageId": "459", "endLine": 23, "endColumn": 24}, {"ruleId": "460", "severity": 1, "message": "465", "line": 64, "column": 6, "nodeType": "462", "endLine": 64, "endColumn": 12, "suggestions": "466"}, {"ruleId": "456", "severity": 1, "message": "467", "line": 21, "column": 10, "nodeType": "458", "messageId": "459", "endLine": 21, "endColumn": 17}, {"ruleId": "460", "severity": 1, "message": "468", "line": 60, "column": 6, "nodeType": "462", "endLine": 60, "endColumn": 18, "suggestions": "469"}, {"ruleId": "470", "severity": 1, "message": "471", "line": 94, "column": 19, "nodeType": "472", "messageId": "473", "endLine": 94, "endColumn": 21}, {"ruleId": "474", "severity": 1, "message": "475", "line": 214, "column": 13, "nodeType": "476", "endLine": 214, "endColumn": 25}, {"ruleId": "474", "severity": 1, "message": "475", "line": 219, "column": 13, "nodeType": "476", "endLine": 219, "endColumn": 25}, {"ruleId": "474", "severity": 1, "message": "475", "line": 224, "column": 13, "nodeType": "476", "endLine": 224, "endColumn": 25}, {"ruleId": "470", "severity": 1, "message": "477", "line": 229, "column": 31, "nodeType": "472", "messageId": "473", "endLine": 229, "endColumn": 33}, {"ruleId": "474", "severity": 1, "message": "475", "line": 236, "column": 13, "nodeType": "476", "endLine": 236, "endColumn": 25}, {"ruleId": "474", "severity": 1, "message": "475", "line": 241, "column": 13, "nodeType": "476", "endLine": 241, "endColumn": 25}, {"ruleId": "460", "severity": 1, "message": "478", "line": 33, "column": 6, "nodeType": "462", "endLine": 33, "endColumn": 8, "suggestions": "479"}, {"ruleId": "456", "severity": 1, "message": "480", "line": 11, "column": 3, "nodeType": "458", "messageId": "459", "endLine": 11, "endColumn": 11}, {"ruleId": "456", "severity": 1, "message": "481", "line": 12, "column": 3, "nodeType": "458", "messageId": "459", "endLine": 12, "endColumn": 9}, {"ruleId": "456", "severity": 1, "message": "482", "line": 13, "column": 3, "nodeType": "458", "messageId": "459", "endLine": 13, "endColumn": 9}, {"ruleId": "456", "severity": 1, "message": "483", "line": 14, "column": 3, "nodeType": "458", "messageId": "459", "endLine": 14, "endColumn": 10}, {"ruleId": "456", "severity": 1, "message": "484", "line": 15, "column": 3, "nodeType": "458", "messageId": "459", "endLine": 15, "endColumn": 8}, {"ruleId": "456", "severity": 1, "message": "485", "line": 16, "column": 3, "nodeType": "458", "messageId": "459", "endLine": 16, "endColumn": 13}, {"ruleId": "456", "severity": 1, "message": "486", "line": 17, "column": 3, "nodeType": "458", "messageId": "459", "endLine": 17, "endColumn": 10}, {"ruleId": "456", "severity": 1, "message": "487", "line": 18, "column": 3, "nodeType": "458", "messageId": "459", "endLine": 18, "endColumn": 10}, {"ruleId": "456", "severity": 1, "message": "488", "line": 19, "column": 3, "nodeType": "458", "messageId": "459", "endLine": 19, "endColumn": 9}, {"ruleId": "456", "severity": 1, "message": "489", "line": 20, "column": 3, "nodeType": "458", "messageId": "459", "endLine": 20, "endColumn": 13}, {"ruleId": "456", "severity": 1, "message": "490", "line": 21, "column": 3, "nodeType": "458", "messageId": "459", "endLine": 21, "endColumn": 9}, {"ruleId": "456", "severity": 1, "message": "491", "line": 22, "column": 3, "nodeType": "458", "messageId": "459", "endLine": 22, "endColumn": 12}, {"ruleId": "456", "severity": 1, "message": "492", "line": 23, "column": 3, "nodeType": "458", "messageId": "459", "endLine": 23, "endColumn": 9}, {"ruleId": "456", "severity": 1, "message": "493", "line": 24, "column": 3, "nodeType": "458", "messageId": "459", "endLine": 24, "endColumn": 14}, {"ruleId": "456", "severity": 1, "message": "494", "line": 25, "column": 3, "nodeType": "458", "messageId": "459", "endLine": 25, "endColumn": 9}, {"ruleId": "456", "severity": 1, "message": "495", "line": 26, "column": 3, "nodeType": "458", "messageId": "459", "endLine": 26, "endColumn": 11}, {"ruleId": "456", "severity": 1, "message": "496", "line": 27, "column": 3, "nodeType": "458", "messageId": "459", "endLine": 27, "endColumn": 13}, {"ruleId": "456", "severity": 1, "message": "497", "line": 28, "column": 3, "nodeType": "458", "messageId": "459", "endLine": 28, "endColumn": 11}, {"ruleId": "456", "severity": 1, "message": "498", "line": 29, "column": 3, "nodeType": "458", "messageId": "459", "endLine": 29, "endColumn": 10}, {"ruleId": "456", "severity": 1, "message": "499", "line": 30, "column": 3, "nodeType": "458", "messageId": "459", "endLine": 30, "endColumn": 14}, {"ruleId": "456", "severity": 1, "message": "500", "line": 50, "column": 10, "nodeType": "458", "messageId": "459", "endLine": 50, "endColumn": 20}, {"ruleId": "456", "severity": 1, "message": "501", "line": 50, "column": 22, "nodeType": "458", "messageId": "459", "endLine": 50, "endColumn": 35}, {"ruleId": "456", "severity": 1, "message": "502", "line": 51, "column": 10, "nodeType": "458", "messageId": "459", "endLine": 51, "endColumn": 20}, {"ruleId": "456", "severity": 1, "message": "503", "line": 51, "column": 22, "nodeType": "458", "messageId": "459", "endLine": 51, "endColumn": 35}, {"ruleId": "456", "severity": 1, "message": "504", "line": 56, "column": 10, "nodeType": "458", "messageId": "459", "endLine": 56, "endColumn": 25}, {"ruleId": "456", "severity": 1, "message": "505", "line": 56, "column": 27, "nodeType": "458", "messageId": "459", "endLine": 56, "endColumn": 45}, {"ruleId": "456", "severity": 1, "message": "506", "line": 58, "column": 10, "nodeType": "458", "messageId": "459", "endLine": 58, "endColumn": 25}, {"ruleId": "456", "severity": 1, "message": "507", "line": 59, "column": 10, "nodeType": "458", "messageId": "459", "endLine": 59, "endColumn": 25}, {"ruleId": "456", "severity": 1, "message": "508", "line": 70, "column": 10, "nodeType": "458", "messageId": "459", "endLine": 70, "endColumn": 22}, {"ruleId": "456", "severity": 1, "message": "509", "line": 70, "column": 24, "nodeType": "458", "messageId": "459", "endLine": 70, "endColumn": 39}, {"ruleId": "456", "severity": 1, "message": "510", "line": 71, "column": 10, "nodeType": "458", "messageId": "459", "endLine": 71, "endColumn": 20}, {"ruleId": "456", "severity": 1, "message": "511", "line": 71, "column": 22, "nodeType": "458", "messageId": "459", "endLine": 71, "endColumn": 35}, {"ruleId": "460", "severity": 1, "message": "512", "line": 178, "column": 6, "nodeType": "462", "endLine": 178, "endColumn": 46, "suggestions": "513"}, {"ruleId": "456", "severity": 1, "message": "514", "line": 366, "column": 9, "nodeType": "458", "messageId": "459", "endLine": 366, "endColumn": 18}, {"ruleId": "460", "severity": 1, "message": "515", "line": 47, "column": 6, "nodeType": "462", "endLine": 47, "endColumn": 12, "suggestions": "516"}, {"ruleId": "470", "severity": 1, "message": "477", "line": 94, "column": 22, "nodeType": "472", "messageId": "473", "endLine": 94, "endColumn": 24}, {"ruleId": "470", "severity": 1, "message": "477", "line": 95, "column": 22, "nodeType": "472", "messageId": "473", "endLine": 95, "endColumn": 24}, {"ruleId": "470", "severity": 1, "message": "477", "line": 96, "column": 22, "nodeType": "472", "messageId": "473", "endLine": 96, "endColumn": 24}, {"ruleId": "456", "severity": 1, "message": "464", "line": 9, "column": 17, "nodeType": "458", "messageId": "459", "endLine": 9, "endColumn": 24}, {"ruleId": "460", "severity": 1, "message": "515", "line": 26, "column": 6, "nodeType": "462", "endLine": 26, "endColumn": 12, "suggestions": "517"}, {"ruleId": "456", "severity": 1, "message": "518", "line": 33, "column": 10, "nodeType": "458", "messageId": "459", "endLine": 33, "endColumn": 14}, {"ruleId": "456", "severity": 1, "message": "519", "line": 33, "column": 16, "nodeType": "458", "messageId": "459", "endLine": 33, "endColumn": 23}, {"ruleId": "456", "severity": 1, "message": "520", "line": 34, "column": 22, "nodeType": "458", "messageId": "459", "endLine": 34, "endColumn": 35}, {"ruleId": "460", "severity": 1, "message": "512", "line": 314, "column": 6, "nodeType": "462", "endLine": 314, "endColumn": 15, "suggestions": "521"}, {"ruleId": "474", "severity": 1, "message": "475", "line": 326, "column": 15, "nodeType": "476", "endLine": 326, "endColumn": 27}, {"ruleId": "474", "severity": 1, "message": "475", "line": 327, "column": 15, "nodeType": "476", "endLine": 327, "endColumn": 27}, {"ruleId": "474", "severity": 1, "message": "475", "line": 466, "column": 23, "nodeType": "476", "endLine": 469, "endColumn": 24}, {"ruleId": "474", "severity": 1, "message": "475", "line": 475, "column": 23, "nodeType": "476", "endLine": 478, "endColumn": 24}, {"ruleId": "522", "severity": 1, "message": "523", "line": 530, "column": 19, "nodeType": "476", "messageId": "524", "endLine": 530, "endColumn": 34}, {"ruleId": "474", "severity": 1, "message": "475", "line": 1024, "column": 17, "nodeType": "476", "endLine": 1024, "endColumn": 50}, {"ruleId": "474", "severity": 1, "message": "475", "line": 1029, "column": 17, "nodeType": "476", "endLine": 1029, "endColumn": 50}, {"ruleId": "474", "severity": 1, "message": "475", "line": 1035, "column": 17, "nodeType": "476", "endLine": 1035, "endColumn": 50}, {"ruleId": "525", "severity": 1, "message": "526", "line": 1048, "column": 13, "nodeType": "476", "messageId": "527", "endLine": 1048, "endColumn": 63, "fix": "528"}, {"ruleId": "522", "severity": 1, "message": "529", "line": 251, "column": 9, "nodeType": "476", "messageId": "524", "endLine": 256, "endColumn": 11}, {"ruleId": "522", "severity": 1, "message": "530", "line": 259, "column": 9, "nodeType": "476", "messageId": "524", "endLine": 266, "endColumn": 11}, {"ruleId": "474", "severity": 1, "message": "475", "line": 287, "column": 47, "nodeType": "476", "endLine": 287, "endColumn": 59}, {"ruleId": "474", "severity": 1, "message": "475", "line": 288, "column": 51, "nodeType": "476", "endLine": 288, "endColumn": 63}, {"ruleId": "531", "severity": 1, "message": "532", "line": 313, "column": 19, "nodeType": "476", "endLine": 313, "endColumn": 66}, {"ruleId": "474", "severity": 1, "message": "533", "line": 386, "column": 17, "nodeType": "476", "endLine": 390, "endColumn": 18}, {"ruleId": "474", "severity": 1, "message": "533", "line": 402, "column": 17, "nodeType": "476", "endLine": 407, "endColumn": 18}, {"ruleId": "474", "severity": 1, "message": "475", "line": 10, "column": 9, "nodeType": "476", "endLine": 10, "endColumn": 69}, {"ruleId": "534", "severity": 1, "message": "535", "line": 10, "column": 17, "nodeType": "536", "messageId": "537", "endLine": 10, "endColumn": 44}, {"ruleId": "460", "severity": 1, "message": "515", "line": 748, "column": 6, "nodeType": "462", "endLine": 748, "endColumn": 21, "suggestions": "538"}, {"ruleId": "456", "severity": 1, "message": "539", "line": 802, "column": 13, "nodeType": "458", "messageId": "459", "endLine": 802, "endColumn": 17}, {"ruleId": "460", "severity": 1, "message": "540", "line": 814, "column": 37, "nodeType": "458", "endLine": 814, "endColumn": 48}, {"ruleId": "470", "severity": 1, "message": "477", "line": 823, "column": 58, "nodeType": "472", "messageId": "473", "endLine": 823, "endColumn": 60}, {"ruleId": "456", "severity": 1, "message": "464", "line": 1001, "column": 17, "nodeType": "458", "messageId": "459", "endLine": 1001, "endColumn": 24}, {"ruleId": "456", "severity": 1, "message": "541", "line": 1003, "column": 9, "nodeType": "458", "messageId": "459", "endLine": 1003, "endColumn": 17}, {"ruleId": "456", "severity": 1, "message": "542", "line": 1005, "column": 10, "nodeType": "458", "messageId": "459", "endLine": 1005, "endColumn": 32}, {"ruleId": "456", "severity": 1, "message": "543", "line": 1008, "column": 10, "nodeType": "458", "messageId": "459", "endLine": 1008, "endColumn": 26}, {"ruleId": "456", "severity": 1, "message": "544", "line": 1011, "column": 10, "nodeType": "458", "messageId": "459", "endLine": 1011, "endColumn": 20}, {"ruleId": "456", "severity": 1, "message": "545", "line": 1011, "column": 22, "nodeType": "458", "messageId": "459", "endLine": 1011, "endColumn": 35}, {"ruleId": "456", "severity": 1, "message": "546", "line": 1015, "column": 10, "nodeType": "458", "messageId": "459", "endLine": 1015, "endColumn": 16}, {"ruleId": "460", "severity": 1, "message": "547", "line": 1032, "column": 6, "nodeType": "462", "endLine": 1032, "endColumn": 15, "suggestions": "548"}, {"ruleId": "456", "severity": 1, "message": "549", "line": 1033, "column": 9, "nodeType": "458", "messageId": "459", "endLine": 1033, "endColumn": 29}, {"ruleId": "456", "severity": 1, "message": "550", "line": 1054, "column": 9, "nodeType": "458", "messageId": "459", "endLine": 1054, "endColumn": 28}, {"ruleId": "456", "severity": 1, "message": "551", "line": 1068, "column": 9, "nodeType": "458", "messageId": "459", "endLine": 1068, "endColumn": 23}, {"ruleId": "456", "severity": 1, "message": "552", "line": 1077, "column": 9, "nodeType": "458", "messageId": "459", "endLine": 1077, "endColumn": 28}, {"ruleId": "456", "severity": 1, "message": "553", "line": 4, "column": 27, "nodeType": "458", "messageId": "459", "endLine": 4, "endColumn": 36}, {"ruleId": "470", "severity": 1, "message": "477", "line": 14, "column": 13, "nodeType": "472", "messageId": "473", "endLine": 14, "endColumn": 15}, {"ruleId": "456", "severity": 1, "message": "553", "line": 1, "column": 24, "nodeType": "458", "messageId": "459", "endLine": 1, "endColumn": 33}, {"ruleId": "456", "severity": 1, "message": "554", "line": 3, "column": 10, "nodeType": "458", "messageId": "459", "endLine": 3, "endColumn": 16}, {"ruleId": "525", "severity": 1, "message": "526", "line": 12, "column": 9, "nodeType": "476", "messageId": "527", "endLine": 12, "endColumn": 41, "fix": "555"}, {"ruleId": "456", "severity": 1, "message": "556", "line": 7, "column": 11, "nodeType": "458", "messageId": "459", "endLine": 7, "endColumn": 15}, {"ruleId": "456", "severity": 1, "message": "557", "line": 10, "column": 14, "nodeType": "458", "messageId": "459", "endLine": 10, "endColumn": 19}, {"ruleId": "456", "severity": 1, "message": "553", "line": 1, "column": 17, "nodeType": "458", "messageId": "459", "endLine": 1, "endColumn": 26}, {"ruleId": "456", "severity": 1, "message": "556", "line": 7, "column": 11, "nodeType": "458", "messageId": "459", "endLine": 7, "endColumn": 15}, {"ruleId": "456", "severity": 1, "message": "464", "line": 7, "column": 17, "nodeType": "458", "messageId": "459", "endLine": 7, "endColumn": 24}, {"ruleId": "456", "severity": 1, "message": "557", "line": 8, "column": 14, "nodeType": "458", "messageId": "459", "endLine": 8, "endColumn": 19}, {"ruleId": "470", "severity": 1, "message": "477", "line": 67, "column": 20, "nodeType": "472", "messageId": "473", "endLine": 67, "endColumn": 22}, {"ruleId": "470", "severity": 1, "message": "471", "line": 9, "column": 18, "nodeType": "472", "messageId": "473", "endLine": 9, "endColumn": 20}, {"ruleId": "460", "severity": 1, "message": "515", "line": 72, "column": 6, "nodeType": "462", "endLine": 72, "endColumn": 9, "suggestions": "558"}, {"ruleId": "456", "severity": 1, "message": "464", "line": 10, "column": 17, "nodeType": "458", "messageId": "459", "endLine": 10, "endColumn": 24}, {"ruleId": "460", "severity": 1, "message": "512", "line": 59, "column": 6, "nodeType": "462", "endLine": 59, "endColumn": 8, "suggestions": "559"}, {"ruleId": "456", "severity": 1, "message": "560", "line": 25, "column": 12, "nodeType": "458", "messageId": "459", "endLine": 25, "endColumn": 23}, {"ruleId": "460", "severity": 1, "message": "540", "line": 60, "column": 39, "nodeType": "458", "endLine": 60, "endColumn": 50}, {"ruleId": "460", "severity": 1, "message": "561", "line": 93, "column": 8, "nodeType": "462", "endLine": 93, "endColumn": 50, "suggestions": "562"}, {"ruleId": "456", "severity": 1, "message": "563", "line": 3, "column": 46, "nodeType": "458", "messageId": "459", "endLine": 3, "endColumn": 56}, {"ruleId": "456", "severity": 1, "message": "564", "line": 4, "column": 10, "nodeType": "458", "messageId": "459", "endLine": 4, "endColumn": 21}, {"ruleId": "460", "severity": 1, "message": "565", "line": 98, "column": 6, "nodeType": "462", "endLine": 98, "endColumn": 24, "suggestions": "566"}, {"ruleId": "456", "severity": 1, "message": "567", "line": 2, "column": 8, "nodeType": "458", "messageId": "459", "endLine": 2, "endColumn": 10}, {"ruleId": "456", "severity": 1, "message": "568", "line": 12, "column": 9, "nodeType": "458", "messageId": "459", "endLine": 12, "endColumn": 23}, {"ruleId": "456", "severity": 1, "message": "569", "line": 101, "column": 13, "nodeType": "458", "messageId": "459", "endLine": 101, "endColumn": 23}, {"ruleId": "456", "severity": 1, "message": "563", "line": 5, "column": 3, "nodeType": "458", "messageId": "459", "endLine": 5, "endColumn": 13}, {"ruleId": "456", "severity": 1, "message": "564", "line": 10, "column": 10, "nodeType": "458", "messageId": "459", "endLine": 10, "endColumn": 21}, {"ruleId": "456", "severity": 1, "message": "570", "line": 36, "column": 13, "nodeType": "458", "messageId": "459", "endLine": 36, "endColumn": 25}, {"ruleId": "456", "severity": 1, "message": "571", "line": 36, "column": 27, "nodeType": "458", "messageId": "459", "endLine": 36, "endColumn": 38}, {"ruleId": "572", "severity": 1, "message": "573", "line": 95, "column": 11, "nodeType": "574", "messageId": "575", "endLine": 95, "endColumn": 20}, {"ruleId": "460", "severity": 1, "message": "540", "line": 107, "column": 39, "nodeType": "458", "endLine": 107, "endColumn": 50}, {"ruleId": "460", "severity": 1, "message": "576", "line": 117, "column": 8, "nodeType": "462", "endLine": 117, "endColumn": 34, "suggestions": "577"}, {"ruleId": "460", "severity": 1, "message": "561", "line": 120, "column": 8, "nodeType": "462", "endLine": 120, "endColumn": 20, "suggestions": "578"}, {"ruleId": "456", "severity": 1, "message": "570", "line": 11, "column": 11, "nodeType": "458", "messageId": "459", "endLine": 11, "endColumn": 23}, {"ruleId": "456", "severity": 1, "message": "571", "line": 11, "column": 25, "nodeType": "458", "messageId": "459", "endLine": 11, "endColumn": 36}, {"ruleId": "460", "severity": 1, "message": "512", "line": 67, "column": 6, "nodeType": "462", "endLine": 67, "endColumn": 15, "suggestions": "579"}, {"ruleId": "470", "severity": 1, "message": "471", "line": 70, "column": 14, "nodeType": "472", "messageId": "473", "endLine": 70, "endColumn": 16}, {"ruleId": "470", "severity": 1, "message": "471", "line": 73, "column": 21, "nodeType": "472", "messageId": "473", "endLine": 73, "endColumn": 23}, {"ruleId": "470", "severity": 1, "message": "477", "line": 74, "column": 11, "nodeType": "472", "messageId": "473", "endLine": 74, "endColumn": 13}, {"ruleId": "470", "severity": 1, "message": "477", "line": 78, "column": 34, "nodeType": "472", "messageId": "473", "endLine": 78, "endColumn": 36}, {"ruleId": "470", "severity": 1, "message": "477", "line": 82, "column": 25, "nodeType": "472", "messageId": "473", "endLine": 82, "endColumn": 27}, {"ruleId": "470", "severity": 1, "message": "477", "line": 94, "column": 57, "nodeType": "472", "messageId": "473", "endLine": 94, "endColumn": 59}, {"ruleId": "470", "severity": 1, "message": "477", "line": 122, "column": 15, "nodeType": "472", "messageId": "473", "endLine": 122, "endColumn": 17}, {"ruleId": "470", "severity": 1, "message": "471", "line": 155, "column": 40, "nodeType": "472", "messageId": "473", "endLine": 155, "endColumn": 42}, {"ruleId": "470", "severity": 1, "message": "471", "line": 389, "column": 36, "nodeType": "472", "messageId": "473", "endLine": 389, "endColumn": 38}, {"ruleId": "460", "severity": 1, "message": "580", "line": 62, "column": 6, "nodeType": "462", "endLine": 62, "endColumn": 15, "suggestions": "581"}, {"ruleId": "470", "severity": 1, "message": "477", "line": 95, "column": 22, "nodeType": "472", "messageId": "473", "endLine": 95, "endColumn": 24}, {"ruleId": "470", "severity": 1, "message": "477", "line": 98, "column": 15, "nodeType": "472", "messageId": "473", "endLine": 98, "endColumn": 17}, {"ruleId": "470", "severity": 1, "message": "471", "line": 119, "column": 18, "nodeType": "472", "messageId": "473", "endLine": 119, "endColumn": 20}, {"ruleId": "470", "severity": 1, "message": "477", "line": 125, "column": 15, "nodeType": "472", "messageId": "473", "endLine": 125, "endColumn": 17}, {"ruleId": "470", "severity": 1, "message": "477", "line": 131, "column": 22, "nodeType": "472", "messageId": "473", "endLine": 131, "endColumn": 24}, {"ruleId": "470", "severity": 1, "message": "477", "line": 137, "column": 22, "nodeType": "472", "messageId": "473", "endLine": 137, "endColumn": 24}, {"ruleId": "470", "severity": 1, "message": "477", "line": 140, "column": 15, "nodeType": "472", "messageId": "473", "endLine": 140, "endColumn": 17}, {"ruleId": "470", "severity": 1, "message": "477", "line": 185, "column": 22, "nodeType": "472", "messageId": "473", "endLine": 185, "endColumn": 24}, {"ruleId": "470", "severity": 1, "message": "477", "line": 188, "column": 15, "nodeType": "472", "messageId": "473", "endLine": 188, "endColumn": 17}, {"ruleId": "582", "severity": 1, "message": "583", "line": 224, "column": 15, "nodeType": "476", "endLine": 232, "endColumn": 17}, {"ruleId": "460", "severity": 1, "message": "515", "line": 41, "column": 6, "nodeType": "462", "endLine": 41, "endColumn": 32, "suggestions": "584"}, {"ruleId": "456", "severity": 1, "message": "585", "line": 44, "column": 10, "nodeType": "458", "messageId": "459", "endLine": 44, "endColumn": 24}, {"ruleId": "460", "severity": 1, "message": "586", "line": 58, "column": 6, "nodeType": "462", "endLine": 58, "endColumn": 8, "suggestions": "587"}, {"ruleId": "456", "severity": 1, "message": "585", "line": 64, "column": 10, "nodeType": "458", "messageId": "459", "endLine": 64, "endColumn": 24}, {"ruleId": "456", "severity": 1, "message": "588", "line": 105, "column": 7, "nodeType": "458", "messageId": "459", "endLine": 105, "endColumn": 12}, {"ruleId": "470", "severity": 1, "message": "477", "line": 134, "column": 22, "nodeType": "472", "messageId": "473", "endLine": 134, "endColumn": 24}, {"ruleId": "470", "severity": 1, "message": "477", "line": 158, "column": 15, "nodeType": "472", "messageId": "473", "endLine": 158, "endColumn": 17}, {"ruleId": "470", "severity": 1, "message": "477", "line": 187, "column": 22, "nodeType": "472", "messageId": "473", "endLine": 187, "endColumn": 24}, {"ruleId": "470", "severity": 1, "message": "471", "line": 194, "column": 32, "nodeType": "472", "messageId": "473", "endLine": 194, "endColumn": 34}, {"ruleId": "456", "severity": 1, "message": "589", "line": 7, "column": 10, "nodeType": "458", "messageId": "459", "endLine": 7, "endColumn": 17}, {"ruleId": "456", "severity": 1, "message": "590", "line": 8, "column": 10, "nodeType": "458", "messageId": "459", "endLine": 8, "endColumn": 18}, {"ruleId": "456", "severity": 1, "message": "591", "line": 1, "column": 10, "nodeType": "458", "messageId": "459", "endLine": 1, "endColumn": 25}, {"ruleId": "456", "severity": 1, "message": "592", "line": 2, "column": 10, "nodeType": "458", "messageId": "459", "endLine": 2, "endColumn": 19}, {"ruleId": "456", "severity": 1, "message": "593", "line": 3, "column": 10, "nodeType": "458", "messageId": "459", "endLine": 3, "endColumn": 19}, {"ruleId": "460", "severity": 1, "message": "468", "line": 41, "column": 6, "nodeType": "462", "endLine": 41, "endColumn": 8, "suggestions": "594"}, {"ruleId": "456", "severity": 1, "message": "585", "line": 44, "column": 10, "nodeType": "458", "messageId": "459", "endLine": 44, "endColumn": 24}, {"ruleId": "456", "severity": 1, "message": "464", "line": 23, "column": 17, "nodeType": "458", "messageId": "459", "endLine": 23, "endColumn": 24}, {"ruleId": "460", "severity": 1, "message": "468", "line": 45, "column": 6, "nodeType": "462", "endLine": 45, "endColumn": 8, "suggestions": "595"}, {"ruleId": "460", "severity": 1, "message": "596", "line": 57, "column": 6, "nodeType": "462", "endLine": 57, "endColumn": 21, "suggestions": "597"}, {"ruleId": "456", "severity": 1, "message": "571", "line": 8, "column": 25, "nodeType": "458", "messageId": "459", "endLine": 8, "endColumn": 36}, {"ruleId": "456", "severity": 1, "message": "464", "line": 9, "column": 17, "nodeType": "458", "messageId": "459", "endLine": 9, "endColumn": 24}, {"ruleId": "460", "severity": 1, "message": "512", "line": 53, "column": 6, "nodeType": "462", "endLine": 53, "endColumn": 8, "suggestions": "598"}, {"ruleId": "470", "severity": 1, "message": "471", "line": 108, "column": 24, "nodeType": "472", "messageId": "473", "endLine": 108, "endColumn": 26}, {"ruleId": "470", "severity": 1, "message": "471", "line": 133, "column": 41, "nodeType": "472", "messageId": "473", "endLine": 133, "endColumn": 43}, {"ruleId": "582", "severity": 1, "message": "583", "line": 315, "column": 13, "nodeType": "476", "endLine": 323, "endColumn": 15}, {"ruleId": "582", "severity": 1, "message": "583", "line": 488, "column": 17, "nodeType": "476", "endLine": 492, "endColumn": 19}, {"ruleId": "456", "severity": 1, "message": "464", "line": 7, "column": 17, "nodeType": "458", "messageId": "459", "endLine": 7, "endColumn": 24}, {"ruleId": "470", "severity": 1, "message": "477", "line": 56, "column": 22, "nodeType": "472", "messageId": "473", "endLine": 56, "endColumn": 24}, {"ruleId": "531", "severity": 1, "message": "532", "line": 148, "column": 23, "nodeType": "476", "endLine": 151, "endColumn": 25}, {"ruleId": "599", "severity": 1, "message": "600", "line": 153, "column": 23, "nodeType": "476", "endLine": 153, "endColumn": 27}, {"ruleId": "460", "severity": 1, "message": "601", "line": 26, "column": 6, "nodeType": "462", "endLine": 26, "endColumn": 14, "suggestions": "602"}, {"ruleId": "456", "severity": 1, "message": "563", "line": 10, "column": 3, "nodeType": "458", "messageId": "459", "endLine": 10, "endColumn": 13}, {"ruleId": "456", "severity": 1, "message": "570", "line": 45, "column": 11, "nodeType": "458", "messageId": "459", "endLine": 45, "endColumn": 23}, {"ruleId": "456", "severity": 1, "message": "571", "line": 45, "column": 25, "nodeType": "458", "messageId": "459", "endLine": 45, "endColumn": 36}, {"ruleId": "460", "severity": 1, "message": "603", "line": 60, "column": 6, "nodeType": "462", "endLine": 60, "endColumn": 15, "suggestions": "604"}, {"ruleId": "460", "severity": 1, "message": "540", "line": 119, "column": 37, "nodeType": "458", "endLine": 119, "endColumn": 48}, {"ruleId": "470", "severity": 1, "message": "477", "line": 128, "column": 58, "nodeType": "472", "messageId": "473", "endLine": 128, "endColumn": 60}, {"ruleId": "456", "severity": 1, "message": "570", "line": 315, "column": 11, "nodeType": "458", "messageId": "459", "endLine": 315, "endColumn": 23}, {"ruleId": "456", "severity": 1, "message": "571", "line": 315, "column": 25, "nodeType": "458", "messageId": "459", "endLine": 315, "endColumn": 36}, {"ruleId": "456", "severity": 1, "message": "464", "line": 316, "column": 17, "nodeType": "458", "messageId": "459", "endLine": 316, "endColumn": 24}, {"ruleId": "456", "severity": 1, "message": "541", "line": 318, "column": 9, "nodeType": "458", "messageId": "459", "endLine": 318, "endColumn": 17}, {"ruleId": "456", "severity": 1, "message": "542", "line": 320, "column": 10, "nodeType": "458", "messageId": "459", "endLine": 320, "endColumn": 32}, {"ruleId": "456", "severity": 1, "message": "543", "line": 323, "column": 10, "nodeType": "458", "messageId": "459", "endLine": 323, "endColumn": 26}, {"ruleId": "456", "severity": 1, "message": "544", "line": 326, "column": 10, "nodeType": "458", "messageId": "459", "endLine": 326, "endColumn": 20}, {"ruleId": "456", "severity": 1, "message": "545", "line": 326, "column": 22, "nodeType": "458", "messageId": "459", "endLine": 326, "endColumn": 35}, {"ruleId": "456", "severity": 1, "message": "546", "line": 330, "column": 10, "nodeType": "458", "messageId": "459", "endLine": 330, "endColumn": 16}, {"ruleId": "460", "severity": 1, "message": "547", "line": 347, "column": 6, "nodeType": "462", "endLine": 347, "endColumn": 15, "suggestions": "605"}, {"ruleId": "456", "severity": 1, "message": "549", "line": 348, "column": 9, "nodeType": "458", "messageId": "459", "endLine": 348, "endColumn": 29}, {"ruleId": "456", "severity": 1, "message": "550", "line": 369, "column": 9, "nodeType": "458", "messageId": "459", "endLine": 369, "endColumn": 28}, {"ruleId": "456", "severity": 1, "message": "551", "line": 383, "column": 9, "nodeType": "458", "messageId": "459", "endLine": 383, "endColumn": 23}, {"ruleId": "456", "severity": 1, "message": "552", "line": 392, "column": 9, "nodeType": "458", "messageId": "459", "endLine": 392, "endColumn": 28}, "no-unused-vars", "'Cookies' is defined but never used.", "Identifier", "unusedVar", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'location.state'. Either include it or remove the dependency array.", "ArrayExpression", ["606"], "'loading' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'fetchEvents', 'startLoading', and 'stopLoading'. Either include them or remove the dependency array.", ["607"], "'refresh' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'startLoading', 'stopLoading', and 'user'. Either include them or remove the dependency array.", ["608"], "eqeqeq", "Expected '!==' and instead saw '!='.", "BinaryExpression", "unexpected", "jsx-a11y/anchor-is-valid", "The href attribute requires a valid value to be accessible. Provide a valid, navigable address as the href value. If you cannot provide a valid href, but still need the element to resemble a link, use a button and change it with appropriate styles. Learn more: https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/blob/HEAD/docs/rules/anchor-is-valid.md", "JSXOpeningElement", "Expected '===' and instead saw '=='.", "React Hook useEffect has a missing dependency: 'fullMessage'. Either include it or remove the dependency array.", ["609"], "'FaSearch' is defined but never used.", "'FaPlus' is defined but never used.", "'FaEdit' is defined but never used.", "'FaTrash' is defined but never used.", "'FaEye' is defined but never used.", "'FaEyeSlash' is defined but never used.", "'FaTimes' is defined but never used.", "'FaCheck' is defined but never used.", "'FaUser' is defined but never used.", "'FaEnvelope' is defined but never used.", "'FaLock' is defined but never used.", "'FaUserTag' is defined but never used.", "'FaCode' is defined but never used.", "'FaRefreshCw' is defined but never used.", "'FaSort' is defined but never used.", "'FaSortUp' is defined but never used.", "'FaSortDown' is defined but never used.", "'FaFilter' is defined but never used.", "'FaUsers' is defined but never used.", "'FaShieldAlt' is defined but never used.", "'sortConfig' is assigned a value but never used.", "'setSortConfig' is assigned a value but never used.", "'filterRole' is assigned a value but never used.", "'setFilterRole' is assigned a value but never used.", "'showCreateModal' is assigned a value but never used.", "'setShowCreateModal' is assigned a value but never used.", "'showDeleteModal' is assigned a value but never used.", "'selectedAccount' is assigned a value but never used.", "'showPassword' is assigned a value but never used.", "'setShowPassword' is assigned a value but never used.", "'formErrors' is assigned a value but never used.", "'setFormErrors' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'user'. Either include it or remove the dependency array.", ["610"], "'sentAgain' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'startLoading' and 'stopLoading'. Either include them or remove the dependency array.", ["611"], ["612"], "'data' is assigned a value but never used.", "'setData' is assigned a value but never used.", "'setTopproduct' is assigned a value but never used.", ["613"], "react/jsx-pascal-case", "Imported JSX component Sales_daily must be in PascalCase or SCREAMING_SNAKE_CASE", "usePascalOrSnakeCase", "react/jsx-no-target-blank", "Using target=\"_blank\" without rel=\"noreferrer\" (which implies rel=\"noopener\") is a security risk in older browsers: see https://mathiasbynens.github.io/rel-noopener/#recommendations", "noTargetBlankWithoutNoreferrer", {"range": "614", "text": "615"}, "Imported JSX component Change_password must be in PascalCase or SCREAMING_SNAKE_CASE", "Imported JSX component Forgot_password must be in PascalCase or SCREAMING_SNAKE_CASE", "jsx-a11y/alt-text", "img elements must have an alt prop, either with meaningful text, or an empty string for decorative images.", "The href attribute is required for an anchor to be keyboard accessible. Provide a valid, navigable address as the href value. If you cannot provide an href, but still need the element to resemble a link, use a button and change it with appropriate styles. Learn more: https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/blob/HEAD/docs/rules/anchor-is-valid.md", "no-script-url", "Script URL is a form of eval.", "Literal", "unexpectedScriptURL", ["616"], "'sugg' is assigned a value but never used.", "React Hook useCallback received a function whose dependencies are unknown. Pass an inline function instead.", "'listItem' is assigned a value but never used.", "'isDropdownOpenSupplier' is assigned a value but never used.", "'selectedSupplier' is assigned a value but never used.", "'quantities' is assigned a value but never used.", "'setQuantities' is assigned a value but never used.", "'isOpen' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'listProductWereAdded' and 'setIdProductAdded'. Either include them or remove the dependency array. If 'setIdProductAdded' changes too often, find the parent component that defines it and wrap that definition in useCallback.", ["617"], "'handleSupplierChange' is assigned a value but never used.", "'handleSupplierClick' is assigned a value but never used.", "'toggleDropdown' is assigned a value but never used.", "'dropdownRefSupplier' is assigned a value but never used.", "'useEffect' is defined but never used.", "'notify' is defined but never used.", {"range": "618", "text": "615"}, "'user' is assigned a value but never used.", "'Setdt' is assigned a value but never used.", ["619"], ["620"], "'selectedRow' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'debouncedFetchSuggestions'. Either include it or remove the dependency array.", ["621"], "'useContext' is defined but never used.", "'AuthContext' is defined but never used.", "React Hook useEffect has missing dependencies: 'getData' and 'getSupplierByOrderId'. Either include them or remove the dependency array.", ["622"], "'io' is defined but never used.", "'messageHandled' is assigned a value but never used.", "'newMessage' is assigned a value but never used.", "'startLoading' is assigned a value but never used.", "'stopLoading' is assigned a value but never used.", "no-unreachable", "Unreachable code.", "ReturnStatement", "unreachableCode", "React Hook useEffect has missing dependencies: 'debouncedFetchSuggestions' and 'searchTerm'. Either include them or remove the dependency array.", ["623"], ["624"], ["625"], "React Hook useEffect has missing dependencies: 'loading', 'reload', 'startLoading', and 'stopLoading'. Either include them or remove the dependency array. If 'reload' changes too often, find the parent component that defines it and wrap that definition in useCallback.", ["626"], "jsx-a11y/img-redundant-alt", "Redundant alt attribute. Screen-readers already announce `img` tags as an image. You don’t need to use the words `image`, `photo,` or `picture` (or any specified custom words) in the alt prop.", ["627"], "'selectedOrders' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'startLoading', 'stopLoading', 'supplier', and 'user'. Either include them or remove the dependency array.", ["628"], "'count' is assigned a value but never used.", "'GrGroup' is defined but never used.", "'FaKeycdn' is defined but never used.", "'RiSettings4Line' is defined but never used.", "'FaRegBell' is defined but never used.", "'FaRegUser' is defined but never used.", ["629"], ["630"], "React Hook useEffect has a missing dependency: 'fetchProducts'. Either include it or remove the dependency array.", ["631"], ["632"], "jsx-a11y/heading-has-content", "Headings must have content and the content must be accessible by a screen reader.", "React Hook useEffect has a missing dependency: 'toggleModal'. Either include it or remove the dependency array.", ["633"], "React Hook useEffect has a missing dependency: 'user.id_owner'. Either include it or remove the dependency array.", ["634"], ["635"], {"desc": "636", "fix": "637"}, {"desc": "638", "fix": "639"}, {"desc": "640", "fix": "641"}, {"desc": "642", "fix": "643"}, {"desc": "644", "fix": "645"}, {"desc": "646", "fix": "647"}, {"desc": "646", "fix": "648"}, {"desc": "649", "fix": "650"}, [40187, 40187], " rel=\"noreferrer\"", {"desc": "651", "fix": "652"}, {"desc": "653", "fix": "654"}, [559, 559], {"desc": "655", "fix": "656"}, {"desc": "657", "fix": "658"}, {"desc": "659", "fix": "660"}, {"desc": "661", "fix": "662"}, {"desc": "663", "fix": "664"}, {"desc": "665", "fix": "666"}, {"desc": "649", "fix": "667"}, {"desc": "668", "fix": "669"}, {"desc": "670", "fix": "671"}, {"desc": "672", "fix": "673"}, {"desc": "646", "fix": "674"}, {"desc": "646", "fix": "675"}, {"desc": "676", "fix": "677"}, {"desc": "657", "fix": "678"}, {"desc": "679", "fix": "680"}, {"desc": "681", "fix": "682"}, {"desc": "653", "fix": "683"}, "Update the dependencies array to be: [location.state]", {"range": "684", "text": "685"}, "Update the dependencies array to be: [fetchEvents, startLoading, stopLoading, user]", {"range": "686", "text": "687"}, "Update the dependencies array to be: [loading, startLoading, stopLoading, user, x]", {"range": "688", "text": "689"}, "Update the dependencies array to be: [fullMessage]", {"range": "690", "text": "691"}, "Update the dependencies array to be: [user.id_owner, user._id, getAccounts, user]", {"range": "692", "text": "693"}, "Update the dependencies array to be: [startLoading, stopLoading, user]", {"range": "694", "text": "695"}, {"range": "696", "text": "695"}, "Update the dependencies array to be: [loading, user]", {"range": "697", "text": "698"}, "Update the dependencies array to be: [loading, startLoading, stopLoading, user]", {"range": "699", "text": "700"}, "Update the dependencies array to be: [dataHis, listProductWereAdded, setIdProductAdded]", {"range": "701", "text": "702"}, "Update the dependencies array to be: [c, startLoading, stopLoading]", {"range": "703", "text": "704"}, "Update the dependencies array to be: [user]", {"range": "705", "text": "706"}, "Update the dependencies array to be: [searchTerm, page, loading, loadLog, user, debouncedFetchSuggestions]", {"range": "707", "text": "708"}, "Update the dependencies array to be: [getData, getSupplierByOrderId, idOrder, loading]", {"range": "709", "text": "710"}, "Update the dependencies array to be: [loading, loadOrder, user, debouncedFetchSuggestions, searchTerm]", {"range": "711", "text": "712"}, "Update the dependencies array to be: [debouncedFetchSuggestions, searchTerm]", {"range": "713", "text": "714"}, {"range": "715", "text": "698"}, "Update the dependencies array to be: [loading, reload, startLoading, stopLoading, user, x]", {"range": "716", "text": "717"}, "Update the dependencies array to be: [customer, startLoading, stopLoading, supplier, user]", {"range": "718", "text": "719"}, "Update the dependencies array to be: [startLoading, stopLoading, supplier, user]", {"range": "720", "text": "721"}, {"range": "722", "text": "695"}, {"range": "723", "text": "695"}, "Update the dependencies array to be: [user, loading, fetchProducts]", {"range": "724", "text": "725"}, {"range": "726", "text": "706"}, "Update the dependencies array to be: [isOpen, toggleModal]", {"range": "727", "text": "728"}, "Update the dependencies array to be: [loading, user.id_owner]", {"range": "729", "text": "730"}, {"range": "731", "text": "702"}, [668, 670], "[location.state]", [1984, 1990], "[fetchEvents, startLoading, stopLoading, user]", [2221, 2233], "[loading, startLoading, stopLoading, user, x]", [1417, 1419], "[fullMessage]", [5899, 5939], "[user.id_owner, user._id, getAccounts, user]", [1388, 1394], "[startLoading, stopLoading, user]", [1012, 1018], [8608, 8617], "[loading, user]", [28450, 28465], "[loading, startLoading, stopLoading, user]", [37985, 37994], "[dataHis, listProductWereAdded, setIdProductAdded]", [1972, 1975], "[c, startLoading, stopLoading]", [2040, 2042], "[user]", [2705, 2747], "[searchTerm, page, loading, loadLog, user, debouncedFetchSuggestions]", [3134, 3152], "[getData, getSupplierByOrderId, idOrder, loading]", [3694, 3720], "[loading, loadOrder, user, debouncedFetchSuggestions, searchTerm]", [3807, 3819], "[debouncedFetchSuggestions, searchTerm]", [2440, 2449], [1763, 1772], "[loading, reload, startLoading, stopLoading, user, x]", [1324, 1350], "[customer, startLoading, stopLoading, supplier, user]", [2025, 2027], "[startLoading, stopLoading, supplier, user]", [1270, 1272], [1458, 1460], [1894, 1909], "[user, loading, fetchProducts]", [2054, 2056], [782, 790], "[isOpen, toggleModal]", [2435, 2444], "[loading, user.id_owner]", [12037, 12046]]