{"ast": null, "code": "var _jsxFileName = \"F:\\\\File\\\\Web\\\\BTLNMCNNPM\\\\Front\\\\NMCNPM_IT3180_20241\\\\website-app\\\\src\\\\components\\\\export\\\\form_show.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from \"react\";\nimport \"../Manage_product/history.css\";\nimport { useAuth } from \"../introduce/useAuth\";\nimport { useLoading } from \"../introduce/Loading\";\nimport CustomerForm from \"./formcustomer\";\nimport { notify } from \"../../components/Notification/notification\";\nimport { default as HistoryComponent } from \"../Manage_product/history.js\";\nimport DeleteProductModal from \"../Manage_product/Form_delete.js\";\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst History = ({\n  turnoff,\n  supplier\n}) => {\n  _s();\n  const {\n    startLoading,\n    stopLoading\n  } = useLoading();\n  const [initialOrders, setInitialOrders] = useState([]);\n  const [formcustomer, setFormcustomer] = useState(false);\n  const [editingIndex, setEditingIndex] = useState(null);\n  const [editedOrder, setEditedOrder] = useState(null);\n  const [deletedOrder, setdeletedOrder] = useState(null);\n  const {\n    user\n  } = useAuth();\n  const [x, setX] = useState(true);\n  const [showhistory, Setshowhistory] = useState(false);\n  const [showdelete, Setshowdelete] = useState(false);\n  useEffect(() => {\n    let body = {\n      user: user\n    };\n    const responses = async () => {\n      startLoading();\n      let response;\n      if (!supplier) {\n        response = await fetch(\"http://localhost:8080/api/sell/getCustomer\", {\n          method: \"POST\",\n          headers: {\n            \"Content-Type\": \"application/json\"\n          },\n          body: JSON.stringify(body)\n        });\n      } else {\n        response = await fetch(\"http://localhost:8080/api/products/getSupplier\", {\n          method: \"POST\",\n          headers: {\n            \"Content-Type\": \"application/json\"\n          },\n          body: JSON.stringify(body)\n        });\n      }\n      let datas = await response.json();\n      console.log(datas);\n      stopLoading();\n      if (!supplier) {\n        setInitialOrders(datas.customers);\n      } else {\n        setInitialOrders(datas.suppliers);\n      }\n    };\n    responses();\n  }, []);\n  const change = () => {\n    setX(!x);\n  };\n  // const [orders, setOrders] = useState(initialOrders);\n  const [searchTerm, setSearchTerm] = useState(\"\");\n  const [selectedOrders, setSelectedOrders] = useState([]);\n  //   Lọc các đơn hàng theo tìm kiếm\n  const filteredOrders = initialOrders.filter(order => {\n    var _order$name;\n    return (order === null || order === void 0 ? void 0 : order.name) && (order === null || order === void 0 ? void 0 : (_order$name = order.name) === null || _order$name === void 0 ? void 0 : _order$name.includes(searchTerm)) || (order === null || order === void 0 ? void 0 : order.email) && (order === null || order === void 0 ? void 0 : order.email.includes(searchTerm)) || (order === null || order === void 0 ? void 0 : order.phone) && (order === null || order === void 0 ? void 0 : order.phone.includes(searchTerm)) || (order === null || order === void 0 ? void 0 : order.money) && (order === null || order === void 0 ? void 0 : order.money.includes(searchTerm));\n  });\n\n  // Cập nhật selectedOrders mỗi khi filteredOrders thay đổi\n  useEffect(() => {\n    setSelectedOrders(new Array(filteredOrders.length).fill(false));\n  }, [filteredOrders.length]); // Chỉ theo dõi độ dài của filteredOrders\n\n  const handleSearch = e => {\n    setSearchTerm(e.target.value);\n  };\n  const onclosecustomer = () => {\n    setFormcustomer(false);\n  };\n  const onformcustomer = () => {\n    setFormcustomer(true);\n  };\n  function formatDateTime(isoString) {\n    const date = new Date(isoString);\n    const year = date.getFullYear();\n    const month = (date.getMonth() + 1).toString().padStart(2, \"0\"); // Tháng tính từ 0 nên phải +1\n    const day = date.getDate().toString().padStart(2, \"0\");\n    const hours = date.getHours().toString().padStart(2, \"0\");\n    const minutes = date.getMinutes().toString().padStart(2, \"0\");\n    const seconds = date.getSeconds().toString().padStart(2, \"0\");\n    return `${hours}:${minutes}:${seconds}, ngày ${day}/${month}/${year}`;\n  }\n  const handleEditClick = (index, order) => {\n    setEditingIndex(index);\n    setEditedOrder({\n      ...order\n    });\n  };\n  let count = 0;\n  const isPhoneValid = phone => {\n    const regex = /^[0-9]+$/; // Kiểm tra chuỗi có 10 chữ số\n    return regex.test(phone);\n  };\n  const handleSaveClick = async () => {\n    if (!isPhoneValid(editedOrder.phone)) {\n      stopLoading();\n      notify(2, \"Số điện thoại không hợp lệ\", \"Thất bại\");\n      return;\n    }\n    startLoading();\n    let url = \"http://localhost:8080/api/sell/editCustomer\";\n    if (supplier) {\n      url = \"http://localhost:8080/api/products/edit_supplier\";\n    }\n    const response = await fetch(url, {\n      method: \"POST\",\n      headers: {\n        \"Content-Type\": \"application/json\"\n      },\n      body: JSON.stringify({\n        user: user,\n        ...(supplier ? {\n          supplier_edit: editedOrder\n        } : {\n          customer_edit: editedOrder\n        })\n      })\n    });\n    const data = await response.json();\n    if (data.message == \"success\") {\n      if (supplier) notify(1, \"supplier đã được cập nhật\", \"Thành công\");else {\n        notify(1, \"khách hàng đã được cập nhật\", \"Thành công\");\n      }\n      const updatedOrders = [...initialOrders];\n      updatedOrders[editingIndex] = editedOrder;\n      setInitialOrders(updatedOrders);\n      setEditingIndex(null); // Thoát khỏi chế độ chỉnh sửa\n    } else {\n      notify(2, data.message, \"Thất bại\");\n    }\n    stopLoading();\n  };\n  const handleCancelClick = () => {\n    setEditingIndex(null); // Hủy chỉnh sửa\n  };\n  const handleEditChange = e => {\n    const {\n      name,\n      value\n    } = e.target;\n    setEditedOrder(prevOrder => ({\n      ...prevOrder,\n      [name]: value\n    }));\n  };\n  const handleEditmoneyChange = e => {\n    const {\n      name,\n      value\n    } = e.target;\n    if (value == \"\") {\n      setEditedOrder(prevOrder => ({\n        ...prevOrder,\n        [name]: 0\n      }));\n      return;\n    }\n    let x = value.replace(/,/g, \"\").replace(/\\./g, \"\");\n    x = parseFloat(x).toLocaleString(\"vi-VN\");\n    setEditedOrder(prevOrder => ({\n      ...prevOrder,\n      [name]: x\n    }));\n  };\n  const delete_action = async (supplier, reason) => {\n    startLoading();\n    let url = \"http://localhost:8080/api/sell/deleteCustomer\";\n    if (supplier) {\n      url = \"http://localhost:8080/api/products/delete_supplier\";\n    }\n    console.log(url);\n    const response = await fetch(url, {\n      method: \"POST\",\n      headers: {\n        \"Content-Type\": \"application/json\"\n      },\n      body: JSON.stringify({\n        user: user,\n        ...(supplier ? {\n          supplier_delete: initialOrders[deletedOrder]\n        } : {\n          customer_delete: initialOrders[deletedOrder]\n        }),\n        detail: reason\n      })\n    });\n    const data = await response.json();\n    if (data.message == \"success\") {\n      if (supplier) notify(1, \"supplier đã xóa\", \"Thành công\");else {\n        notify(1, \"khách hàng đã xóa\", \"Thành công\");\n      }\n      let updatedOrders = [...initialOrders];\n      updatedOrders = updatedOrders.filter((item, index) => index != deletedOrder);\n      setInitialOrders(updatedOrders);\n    } else {\n      notify(2, data.message, \"Thất bại\");\n    }\n    stopLoading();\n  };\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [showdelete && /*#__PURE__*/_jsxDEV(DeleteProductModal, {\n      supplier: supplier ? showdelete : undefined,\n      customer: supplier ? undefined : showdelete,\n      onClose2: () => {\n        Setshowdelete(false);\n      },\n      onDelete: delete_action\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 205,\n      columnNumber: 9\n    }, this), showhistory && /*#__PURE__*/_jsxDEV(HistoryComponent, {\n      turnoff: () => {\n        Setshowhistory(false);\n      },\n      supplier: supplier ? supplier : undefined,\n      customer: supplier ? undefined : true\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 215,\n      columnNumber: 9\n    }, this), formcustomer && /*#__PURE__*/_jsxDEV(CustomerForm, {\n      close: onclosecustomer,\n      change: change,\n      supplier: supplier\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 224,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"history-mgmt-main\",\n      style: {\n        zIndex: 999\n      },\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"history-mgmt-container\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"close\",\n          onClick: turnoff,\n          children: \"x\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 232,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"history-mgmt-header\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"history-mgmt-title\",\n            children: !supplier ? \"Khách hàng\" : \"Nhà cung cấp\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 236,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"history-mgmt-header-controls\",\n            children: [/*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              className: \"history-mgmt-search\",\n              placeholder: \"Search for...\",\n              value: searchTerm,\n              onChange: handleSearch\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 240,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"order-mgmt-history-btn\",\n              style: {\n                marginLeft: \"0px\",\n                marginRight: \"20px\"\n              },\n              onClick: () => {\n                Setshowhistory(true);\n              },\n              children: \"L\\u1ECBch s\\u1EED thay \\u0111\\u1ED1i\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 247,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"order-mgmt-create-btn\",\n              style: {\n                marginLeft: \"0px\",\n                marginRight: \"20px\"\n              },\n              onClick: onformcustomer,\n              children: !supplier ? \"Create customer\" : \"Create supplier\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 256,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 239,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 235,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"table\", {\n          className: \"history-mgmt-table\",\n          children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n            children: /*#__PURE__*/_jsxDEV(\"tr\", {\n              children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"T\\xEAn ng\\u01B0\\u1EDDi t\\u1EA1o\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 269,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"Date\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 270,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                children: !supplier ? \"Tên khách hàng\" : \"Tên nhà cung cấp\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 271,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"phone\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 272,\n                columnNumber: 17\n              }, this), !supplier ? /*#__PURE__*/_jsxDEV(_Fragment, {\n                children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                  children: \"rate\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 275,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  children: \"money\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 276,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true) : /*#__PURE__*/_jsxDEV(_Fragment, {}, void 0, false), /*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"Actions\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 281,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 268,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 267,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n            children: filteredOrders.map((order, index) => /*#__PURE__*/_jsxDEV(\"tr\", {\n              children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                children: [order === null || order === void 0 ? void 0 : order.creater.name, /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 289,\n                  columnNumber: 21\n                }, this), \" \", /*#__PURE__*/_jsxDEV(\"small\", {\n                  children: order === null || order === void 0 ? void 0 : order.creater.email\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 289,\n                  columnNumber: 28\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 287,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                children: formatDateTime(order === null || order === void 0 ? void 0 : order.createdAt)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 291,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                children: editingIndex === index ? /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"text\",\n                    name: \"name\",\n                    value: editedOrder.name,\n                    onChange: handleEditChange\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 295,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"text\",\n                    name: \"email\",\n                    value: editedOrder.email,\n                    onChange: handleEditChange\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 301,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 294,\n                  columnNumber: 23\n                }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [order === null || order === void 0 ? void 0 : order.name, \" \", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 310,\n                    columnNumber: 39\n                  }, this), \" \", /*#__PURE__*/_jsxDEV(\"small\", {\n                    children: order === null || order === void 0 ? void 0 : order.email\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 310,\n                    columnNumber: 46\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 309,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 292,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                children: /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: `history-mgmt-status ${order === null || order === void 0 ? void 0 : order.action}`,\n                  children: editingIndex === index ? /*#__PURE__*/_jsxDEV(\"div\", {\n                    children: /*#__PURE__*/_jsxDEV(\"input\", {\n                      type: \"text\",\n                      name: \"phone\",\n                      value: editedOrder.phone,\n                      onChange: handleEditChange\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 318,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 317,\n                    columnNumber: 25\n                  }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                    children: order === null || order === void 0 ? void 0 : order.phone\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 326,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 315,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 314,\n                columnNumber: 19\n              }, this), !supplier ? /*#__PURE__*/_jsxDEV(_Fragment, {\n                children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                  children: editingIndex === index ? /*#__PURE__*/_jsxDEV(\"div\", {\n                    children: /*#__PURE__*/_jsxDEV(\"input\", {\n                      type: \"text\",\n                      name: \"rate\",\n                      value: editedOrder.rate,\n                      onChange: handleEditChange\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 335,\n                      columnNumber: 29\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 334,\n                    columnNumber: 27\n                  }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                    children: order === null || order === void 0 ? void 0 : order.rate\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 343,\n                    columnNumber: 27\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 332,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  children: editingIndex === index ? /*#__PURE__*/_jsxDEV(\"div\", {\n                    children: /*#__PURE__*/_jsxDEV(\"input\", {\n                      type: \"text\",\n                      name: \"money\",\n                      value: editedOrder.money,\n                      onChange: handleEditmoneyChange\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 349,\n                      columnNumber: 29\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 348,\n                    columnNumber: 27\n                  }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                    children: order === null || order === void 0 ? void 0 : order.money\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 357,\n                    columnNumber: 27\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 346,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true) : /*#__PURE__*/_jsxDEV(_Fragment, {}, void 0, false), /*#__PURE__*/_jsxDEV(\"td\", {\n                children: editingIndex === index ? /*#__PURE__*/_jsxDEV(_Fragment, {\n                  children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                    className: \"order-mgmt-button save\",\n                    onClick: handleSaveClick,\n                    children: \"Save\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 367,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    className: \"order-mgmt-button cancel\",\n                    onClick: handleCancelClick,\n                    children: \"Cancel\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 373,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true) : /*#__PURE__*/_jsxDEV(_Fragment, {\n                  children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                    className: \"order-mgmt-button edit\",\n                    onClick: () => handleEditClick(index, order),\n                    children: \"\\u270F\\uFE0F\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 382,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    className: \"order-mgmt-button delete\",\n                    onClick: () => {\n                      Setshowdelete(order);\n                      setdeletedOrder(index);\n                    },\n                    children: \"\\uD83D\\uDDD1\\uFE0F\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 388,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 364,\n                columnNumber: 19\n              }, this)]\n            }, index, true, {\n              fileName: _jsxFileName,\n              lineNumber: 286,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 284,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 266,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 231,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 230,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true);\n};\n_s(History, \"DPytux9DYqsDCrzK9PnlibY1TZ4=\", false, function () {\n  return [useLoading, useAuth];\n});\n_c = History;\nexport default History;\nvar _c;\n$RefreshReg$(_c, \"History\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useAuth", "useLoading", "CustomerForm", "notify", "default", "HistoryComponent", "DeleteProductModal", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "History", "turnoff", "supplier", "_s", "startLoading", "stopLoading", "initialOrders", "setInitialOrders", "<PERSON><PERSON><PERSON><PERSON>", "set<PERSON><PERSON><PERSON><PERSON><PERSON>", "editingIndex", "setEditingIndex", "editedOrder", "setEditedOrder", "deletedOrder", "setdeletedOrder", "user", "x", "setX", "showhistory", "Setshowhistory", "showdelete", "Setshowdelete", "body", "responses", "response", "fetch", "method", "headers", "JSON", "stringify", "datas", "json", "console", "log", "customers", "suppliers", "change", "searchTerm", "setSearchTerm", "selectedOrders", "setSelectedOrders", "filteredOrders", "filter", "order", "_order$name", "name", "includes", "email", "phone", "money", "Array", "length", "fill", "handleSearch", "e", "target", "value", "onclosecus<PERSON><PERSON>", "onformcus<PERSON><PERSON>", "formatDateTime", "isoString", "date", "Date", "year", "getFullYear", "month", "getMonth", "toString", "padStart", "day", "getDate", "hours", "getHours", "minutes", "getMinutes", "seconds", "getSeconds", "handleEditClick", "index", "count", "isPhoneValid", "regex", "test", "handleSaveClick", "url", "supplier_edit", "customer_edit", "data", "message", "updatedOrders", "handleCancelClick", "handleEditChange", "prevOrder", "handleEditmoneyChange", "replace", "parseFloat", "toLocaleString", "delete_action", "reason", "supplier_delete", "customer_delete", "detail", "item", "children", "undefined", "customer", "onClose2", "onDelete", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "close", "className", "style", "zIndex", "onClick", "type", "placeholder", "onChange", "marginLeft", "marginRight", "map", "creater", "createdAt", "action", "rate", "_c", "$RefreshReg$"], "sources": ["F:/File/Web/BTLNMCNNPM/Front/NMCNPM_IT3180_20241/website-app/src/components/export/form_show.js"], "sourcesContent": ["import React, { useState, useEffect } from \"react\";\r\nimport \"../Manage_product/history.css\";\r\nimport { useAuth } from \"../introduce/useAuth\";\r\nimport { useLoading } from \"../introduce/Loading\";\r\nimport CustomerForm from \"./formcustomer\";\r\nimport { notify } from \"../../components/Notification/notification\";\r\nimport { default as HistoryComponent } from \"../Manage_product/history.js\";\r\nimport DeleteProductModal from \"../Manage_product/Form_delete.js\";\r\nconst History = ({ turnoff, supplier }) => {\r\n  const { startLoading, stopLoading } = useLoading();\r\n  const [initialOrders, setInitialOrders] = useState([]);\r\n  const [formcustomer, setFormcustomer] = useState(false);\r\n  const [editingIndex, setEditingIndex] = useState(null);\r\n  const [editedOrder, setEditedOrder] = useState(null);\r\n  const [deletedOrder, setdeletedOrder] = useState(null);\r\n  const { user } = useAuth();\r\n  const [x, setX] = useState(true);\r\n  const [showhistory, Setshowhistory] = useState(false);\r\n  const [showdelete, Setshowdelete] = useState(false);\r\n  useEffect(() => {\r\n    let body = {\r\n      user: user,\r\n    };\r\n    const responses = async () => {\r\n      startLoading();\r\n      let response;\r\n      if (!supplier) {\r\n        response = await fetch(\"http://localhost:8080/api/sell/getCustomer\", {\r\n          method: \"POST\",\r\n          headers: {\r\n            \"Content-Type\": \"application/json\",\r\n          },\r\n          body: JSON.stringify(body),\r\n        });\r\n      } else {\r\n        response = await fetch(\r\n          \"http://localhost:8080/api/products/getSupplier\",\r\n          {\r\n            method: \"POST\",\r\n            headers: {\r\n              \"Content-Type\": \"application/json\",\r\n            },\r\n            body: JSON.stringify(body),\r\n          }\r\n        );\r\n      }\r\n\r\n      let datas = await response.json();\r\n      console.log(datas);\r\n      stopLoading();\r\n      if (!supplier) {\r\n        setInitialOrders(datas.customers);\r\n      } else {\r\n        setInitialOrders(datas.suppliers);\r\n      }\r\n    };\r\n    responses();\r\n  }, []);\r\n  const change = () => {\r\n    setX(!x);\r\n  };\r\n  // const [orders, setOrders] = useState(initialOrders);\r\n  const [searchTerm, setSearchTerm] = useState(\"\");\r\n  const [selectedOrders, setSelectedOrders] = useState([]);\r\n  //   Lọc các đơn hàng theo tìm kiếm\r\n  const filteredOrders = initialOrders.filter(\r\n    (order) =>\r\n      (order?.name && order?.name?.includes(searchTerm)) ||\r\n      (order?.email && order?.email.includes(searchTerm)) ||\r\n      (order?.phone && order?.phone.includes(searchTerm)) ||\r\n      (order?.money && order?.money.includes(searchTerm))\r\n  );\r\n\r\n  // Cập nhật selectedOrders mỗi khi filteredOrders thay đổi\r\n  useEffect(() => {\r\n    setSelectedOrders(new Array(filteredOrders.length).fill(false));\r\n  }, [filteredOrders.length]); // Chỉ theo dõi độ dài của filteredOrders\r\n\r\n  const handleSearch = (e) => {\r\n    setSearchTerm(e.target.value);\r\n  };\r\n  const onclosecustomer = () => {\r\n    setFormcustomer(false);\r\n  };\r\n  const onformcustomer = () => {\r\n    setFormcustomer(true);\r\n  };\r\n  function formatDateTime(isoString) {\r\n    const date = new Date(isoString);\r\n\r\n    const year = date.getFullYear();\r\n    const month = (date.getMonth() + 1).toString().padStart(2, \"0\"); // Tháng tính từ 0 nên phải +1\r\n    const day = date.getDate().toString().padStart(2, \"0\");\r\n\r\n    const hours = date.getHours().toString().padStart(2, \"0\");\r\n    const minutes = date.getMinutes().toString().padStart(2, \"0\");\r\n    const seconds = date.getSeconds().toString().padStart(2, \"0\");\r\n\r\n    return `${hours}:${minutes}:${seconds}, ngày ${day}/${month}/${year}`;\r\n  }\r\n  const handleEditClick = (index, order) => {\r\n    setEditingIndex(index);\r\n    setEditedOrder({ ...order });\r\n  };\r\n  let count = 0;\r\n  const isPhoneValid = (phone) => {\r\n    const regex = /^[0-9]+$/; // Kiểm tra chuỗi có 10 chữ số\r\n    return regex.test(phone);\r\n  };\r\n  const handleSaveClick = async () => {\r\n    if (!isPhoneValid(editedOrder.phone)) {\r\n      stopLoading();\r\n      notify(2, \"Số điện thoại không hợp lệ\", \"Thất bại\");\r\n      return;\r\n    }\r\n    startLoading();\r\n    let url = \"http://localhost:8080/api/sell/editCustomer\";\r\n    if (supplier) {\r\n      url = \"http://localhost:8080/api/products/edit_supplier\";\r\n    }\r\n    const response = await fetch(url, {\r\n      method: \"POST\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n      body: JSON.stringify({\r\n        user: user,\r\n        ...(supplier\r\n          ? { supplier_edit: editedOrder }\r\n          : { customer_edit: editedOrder }),\r\n      }),\r\n    });\r\n    const data = await response.json();\r\n    if (data.message == \"success\") {\r\n      if (supplier) notify(1, \"supplier đã được cập nhật\", \"Thành công\");\r\n      else {\r\n        notify(1, \"khách hàng đã được cập nhật\", \"Thành công\");\r\n      }\r\n      const updatedOrders = [...initialOrders];\r\n      updatedOrders[editingIndex] = editedOrder;\r\n      setInitialOrders(updatedOrders);\r\n      setEditingIndex(null); // Thoát khỏi chế độ chỉnh sửa\r\n    } else {\r\n      notify(2, data.message, \"Thất bại\");\r\n    }\r\n    stopLoading();\r\n  };\r\n\r\n  const handleCancelClick = () => {\r\n    setEditingIndex(null); // Hủy chỉnh sửa\r\n  };\r\n  const handleEditChange = (e) => {\r\n    const { name, value } = e.target;\r\n    setEditedOrder((prevOrder) => ({ ...prevOrder, [name]: value }));\r\n  };\r\n  const handleEditmoneyChange = (e) => {\r\n    const { name, value } = e.target;\r\n    if (value == \"\") {\r\n      setEditedOrder((prevOrder) => ({ ...prevOrder, [name]: 0 }));\r\n      return;\r\n    }\r\n    let x = value.replace(/,/g, \"\").replace(/\\./g, \"\");\r\n    x = parseFloat(x).toLocaleString(\"vi-VN\");\r\n    setEditedOrder((prevOrder) => ({ ...prevOrder, [name]: x }));\r\n  };\r\n  const delete_action = async (supplier, reason) => {\r\n    startLoading();\r\n    let url = \"http://localhost:8080/api/sell/deleteCustomer\";\r\n    if (supplier) {\r\n      url = \"http://localhost:8080/api/products/delete_supplier\";\r\n    }\r\n    console.log(url);\r\n    const response = await fetch(url, {\r\n      method: \"POST\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n      body: JSON.stringify({\r\n        user: user,\r\n        ...(supplier\r\n          ? { supplier_delete: initialOrders[deletedOrder] }\r\n          : { customer_delete: initialOrders[deletedOrder] }),\r\n        detail: reason,\r\n      }),\r\n    });\r\n    const data = await response.json();\r\n    if (data.message == \"success\") {\r\n      if (supplier) notify(1, \"supplier đã xóa\", \"Thành công\");\r\n      else {\r\n        notify(1, \"khách hàng đã xóa\", \"Thành công\");\r\n      }\r\n      let updatedOrders = [...initialOrders];\r\n      updatedOrders = updatedOrders.filter(\r\n        (item, index) => index != deletedOrder\r\n      );\r\n      setInitialOrders(updatedOrders);\r\n    } else {\r\n      notify(2, data.message, \"Thất bại\");\r\n    }\r\n    stopLoading();\r\n  };\r\n  return (\r\n    <>\r\n      {showdelete && (\r\n        <DeleteProductModal\r\n          supplier={supplier ? showdelete : undefined}\r\n          customer={supplier ? undefined : showdelete}\r\n          onClose2={() => {\r\n            Setshowdelete(false);\r\n          }}\r\n          onDelete={delete_action}\r\n        />\r\n      )}\r\n      {showhistory && (\r\n        <HistoryComponent\r\n          turnoff={() => {\r\n            Setshowhistory(false);\r\n          }}\r\n          supplier={supplier ? supplier : undefined}\r\n          customer={supplier ? undefined : true}\r\n        />\r\n      )}\r\n      {formcustomer && (\r\n        <CustomerForm\r\n          close={onclosecustomer}\r\n          change={change}\r\n          supplier={supplier}\r\n        />\r\n      )}\r\n      <div className=\"history-mgmt-main\" style={{ zIndex: 999 }}>\r\n        <div className=\"history-mgmt-container\">\r\n          <div className=\"close\" onClick={turnoff}>\r\n            x\r\n          </div>\r\n          <div className=\"history-mgmt-header\">\r\n            <h2 className=\"history-mgmt-title\">\r\n              {!supplier ? \"Khách hàng\" : \"Nhà cung cấp\"}\r\n            </h2>\r\n            <div className=\"history-mgmt-header-controls\">\r\n              <input\r\n                type=\"text\"\r\n                className=\"history-mgmt-search\"\r\n                placeholder=\"Search for...\"\r\n                value={searchTerm}\r\n                onChange={handleSearch}\r\n              />\r\n              <button\r\n                className=\"order-mgmt-history-btn\"\r\n                style={{ marginLeft: \"0px\", marginRight: \"20px\" }}\r\n                onClick={() => {\r\n                  Setshowhistory(true);\r\n                }}\r\n              >\r\n                Lịch sử thay đối\r\n              </button>\r\n              <button\r\n                className=\"order-mgmt-create-btn\"\r\n                style={{ marginLeft: \"0px\", marginRight: \"20px\" }}\r\n                onClick={onformcustomer}\r\n              >\r\n                {!supplier ? \"Create customer\" : \"Create supplier\"}\r\n              </button>\r\n            </div>\r\n          </div>\r\n\r\n          <table className=\"history-mgmt-table\">\r\n            <thead>\r\n              <tr>\r\n                <th>Tên người tạo</th>\r\n                <th>Date</th>\r\n                <th>{!supplier ? \"Tên khách hàng\" : \"Tên nhà cung cấp\"}</th>\r\n                <th>phone</th>\r\n                {!supplier ? (\r\n                  <>\r\n                    <th>rate</th>\r\n                    <th>money</th>\r\n                  </>\r\n                ) : (\r\n                  <></>\r\n                )}\r\n                <th>Actions</th>\r\n              </tr>\r\n            </thead>\r\n            <tbody>\r\n              {filteredOrders.map((order, index) => (\r\n                <tr key={index}>\r\n                  <td>\r\n                    {order?.creater.name}\r\n                    <br /> <small>{order?.creater.email}</small>\r\n                  </td>\r\n                  <td>{formatDateTime(order?.createdAt)}</td>\r\n                  <td>\r\n                    {editingIndex === index ? (\r\n                      <div>\r\n                        <input\r\n                          type=\"text\"\r\n                          name=\"name\"\r\n                          value={editedOrder.name}\r\n                          onChange={handleEditChange}\r\n                        />\r\n                        <input\r\n                          type=\"text\"\r\n                          name=\"email\"\r\n                          value={editedOrder.email}\r\n                          onChange={handleEditChange}\r\n                        />\r\n                      </div>\r\n                    ) : (\r\n                      <div>\r\n                        {order?.name} <br /> <small>{order?.email}</small>\r\n                      </div>\r\n                    )}\r\n                  </td>\r\n                  <td>\r\n                    <span className={`history-mgmt-status ${order?.action}`}>\r\n                      {editingIndex === index ? (\r\n                        <div>\r\n                          <input\r\n                            type=\"text\"\r\n                            name=\"phone\"\r\n                            value={editedOrder.phone}\r\n                            onChange={handleEditChange}\r\n                          />\r\n                        </div>\r\n                      ) : (\r\n                        <div>{order?.phone}</div>\r\n                      )}\r\n                    </span>\r\n                  </td>\r\n                  {!supplier ? (\r\n                    <>\r\n                      <td>\r\n                        {editingIndex === index ? (\r\n                          <div>\r\n                            <input\r\n                              type=\"text\"\r\n                              name=\"rate\"\r\n                              value={editedOrder.rate}\r\n                              onChange={handleEditChange}\r\n                            />\r\n                          </div>\r\n                        ) : (\r\n                          <div>{order?.rate}</div>\r\n                        )}\r\n                      </td>\r\n                      <td>\r\n                        {editingIndex === index ? (\r\n                          <div>\r\n                            <input\r\n                              type=\"text\"\r\n                              name=\"money\"\r\n                              value={editedOrder.money}\r\n                              onChange={handleEditmoneyChange}\r\n                            />\r\n                          </div>\r\n                        ) : (\r\n                          <div>{order?.money}</div>\r\n                        )}\r\n                      </td>\r\n                    </>\r\n                  ) : (\r\n                    <></>\r\n                  )}\r\n                  <td>\r\n                    {editingIndex === index ? (\r\n                      <>\r\n                        <button\r\n                          className=\"order-mgmt-button save\"\r\n                          onClick={handleSaveClick}\r\n                        >\r\n                          Save\r\n                        </button>\r\n                        <button\r\n                          className=\"order-mgmt-button cancel\"\r\n                          onClick={handleCancelClick}\r\n                        >\r\n                          Cancel\r\n                        </button>\r\n                      </>\r\n                    ) : (\r\n                      <>\r\n                        <button\r\n                          className=\"order-mgmt-button edit\"\r\n                          onClick={() => handleEditClick(index, order)}\r\n                        >\r\n                          ✏️\r\n                        </button>\r\n                        <button\r\n                          className=\"order-mgmt-button delete\"\r\n                          onClick={() => {\r\n                            Setshowdelete(order);\r\n                            setdeletedOrder(index);\r\n                          }}\r\n                        >\r\n                          🗑️\r\n                        </button>\r\n                      </>\r\n                    )}\r\n                  </td>\r\n                </tr>\r\n              ))}\r\n            </tbody>\r\n          </table>\r\n        </div>\r\n      </div>\r\n    </>\r\n  );\r\n};\r\n\r\nexport default History;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,OAAO,+BAA+B;AACtC,SAASC,OAAO,QAAQ,sBAAsB;AAC9C,SAASC,UAAU,QAAQ,sBAAsB;AACjD,OAAOC,YAAY,MAAM,gBAAgB;AACzC,SAASC,MAAM,QAAQ,4CAA4C;AACnE,SAASC,OAAO,IAAIC,gBAAgB,QAAQ,8BAA8B;AAC1E,OAAOC,kBAAkB,MAAM,kCAAkC;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAClE,MAAMC,OAAO,GAAGA,CAAC;EAAEC,OAAO;EAAEC;AAAS,CAAC,KAAK;EAAAC,EAAA;EACzC,MAAM;IAAEC,YAAY;IAAEC;EAAY,CAAC,GAAGf,UAAU,CAAC,CAAC;EAClD,MAAM,CAACgB,aAAa,EAAEC,gBAAgB,CAAC,GAAGpB,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAACqB,YAAY,EAAEC,eAAe,CAAC,GAAGtB,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACuB,YAAY,EAAEC,eAAe,CAAC,GAAGxB,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAACyB,WAAW,EAAEC,cAAc,CAAC,GAAG1B,QAAQ,CAAC,IAAI,CAAC;EACpD,MAAM,CAAC2B,YAAY,EAAEC,eAAe,CAAC,GAAG5B,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM;IAAE6B;EAAK,CAAC,GAAG3B,OAAO,CAAC,CAAC;EAC1B,MAAM,CAAC4B,CAAC,EAAEC,IAAI,CAAC,GAAG/B,QAAQ,CAAC,IAAI,CAAC;EAChC,MAAM,CAACgC,WAAW,EAAEC,cAAc,CAAC,GAAGjC,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACkC,UAAU,EAAEC,aAAa,CAAC,GAAGnC,QAAQ,CAAC,KAAK,CAAC;EACnDC,SAAS,CAAC,MAAM;IACd,IAAImC,IAAI,GAAG;MACTP,IAAI,EAAEA;IACR,CAAC;IACD,MAAMQ,SAAS,GAAG,MAAAA,CAAA,KAAY;MAC5BpB,YAAY,CAAC,CAAC;MACd,IAAIqB,QAAQ;MACZ,IAAI,CAACvB,QAAQ,EAAE;QACbuB,QAAQ,GAAG,MAAMC,KAAK,CAAC,4CAA4C,EAAE;UACnEC,MAAM,EAAE,MAAM;UACdC,OAAO,EAAE;YACP,cAAc,EAAE;UAClB,CAAC;UACDL,IAAI,EAAEM,IAAI,CAACC,SAAS,CAACP,IAAI;QAC3B,CAAC,CAAC;MACJ,CAAC,MAAM;QACLE,QAAQ,GAAG,MAAMC,KAAK,CACpB,gDAAgD,EAChD;UACEC,MAAM,EAAE,MAAM;UACdC,OAAO,EAAE;YACP,cAAc,EAAE;UAClB,CAAC;UACDL,IAAI,EAAEM,IAAI,CAACC,SAAS,CAACP,IAAI;QAC3B,CACF,CAAC;MACH;MAEA,IAAIQ,KAAK,GAAG,MAAMN,QAAQ,CAACO,IAAI,CAAC,CAAC;MACjCC,OAAO,CAACC,GAAG,CAACH,KAAK,CAAC;MAClB1B,WAAW,CAAC,CAAC;MACb,IAAI,CAACH,QAAQ,EAAE;QACbK,gBAAgB,CAACwB,KAAK,CAACI,SAAS,CAAC;MACnC,CAAC,MAAM;QACL5B,gBAAgB,CAACwB,KAAK,CAACK,SAAS,CAAC;MACnC;IACF,CAAC;IACDZ,SAAS,CAAC,CAAC;EACb,CAAC,EAAE,EAAE,CAAC;EACN,MAAMa,MAAM,GAAGA,CAAA,KAAM;IACnBnB,IAAI,CAAC,CAACD,CAAC,CAAC;EACV,CAAC;EACD;EACA,MAAM,CAACqB,UAAU,EAAEC,aAAa,CAAC,GAAGpD,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACqD,cAAc,EAAEC,iBAAiB,CAAC,GAAGtD,QAAQ,CAAC,EAAE,CAAC;EACxD;EACA,MAAMuD,cAAc,GAAGpC,aAAa,CAACqC,MAAM,CACxCC,KAAK;IAAA,IAAAC,WAAA;IAAA,OACH,CAAAD,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEE,IAAI,MAAIF,KAAK,aAALA,KAAK,wBAAAC,WAAA,GAALD,KAAK,CAAEE,IAAI,cAAAD,WAAA,uBAAXA,WAAA,CAAaE,QAAQ,CAACT,UAAU,CAAC,KAChD,CAAAM,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEI,KAAK,MAAIJ,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEI,KAAK,CAACD,QAAQ,CAACT,UAAU,CAAC,CAAC,IAClD,CAAAM,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEK,KAAK,MAAIL,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEK,KAAK,CAACF,QAAQ,CAACT,UAAU,CAAC,CAAC,IAClD,CAAAM,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEM,KAAK,MAAIN,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEM,KAAK,CAACH,QAAQ,CAACT,UAAU,CAAC,CAAC;EAAA,CACvD,CAAC;;EAED;EACAlD,SAAS,CAAC,MAAM;IACdqD,iBAAiB,CAAC,IAAIU,KAAK,CAACT,cAAc,CAACU,MAAM,CAAC,CAACC,IAAI,CAAC,KAAK,CAAC,CAAC;EACjE,CAAC,EAAE,CAACX,cAAc,CAACU,MAAM,CAAC,CAAC,CAAC,CAAC;;EAE7B,MAAME,YAAY,GAAIC,CAAC,IAAK;IAC1BhB,aAAa,CAACgB,CAAC,CAACC,MAAM,CAACC,KAAK,CAAC;EAC/B,CAAC;EACD,MAAMC,eAAe,GAAGA,CAAA,KAAM;IAC5BjD,eAAe,CAAC,KAAK,CAAC;EACxB,CAAC;EACD,MAAMkD,cAAc,GAAGA,CAAA,KAAM;IAC3BlD,eAAe,CAAC,IAAI,CAAC;EACvB,CAAC;EACD,SAASmD,cAAcA,CAACC,SAAS,EAAE;IACjC,MAAMC,IAAI,GAAG,IAAIC,IAAI,CAACF,SAAS,CAAC;IAEhC,MAAMG,IAAI,GAAGF,IAAI,CAACG,WAAW,CAAC,CAAC;IAC/B,MAAMC,KAAK,GAAG,CAACJ,IAAI,CAACK,QAAQ,CAAC,CAAC,GAAG,CAAC,EAAEC,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;IACjE,MAAMC,GAAG,GAAGR,IAAI,CAACS,OAAO,CAAC,CAAC,CAACH,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;IAEtD,MAAMG,KAAK,GAAGV,IAAI,CAACW,QAAQ,CAAC,CAAC,CAACL,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;IACzD,MAAMK,OAAO,GAAGZ,IAAI,CAACa,UAAU,CAAC,CAAC,CAACP,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;IAC7D,MAAMO,OAAO,GAAGd,IAAI,CAACe,UAAU,CAAC,CAAC,CAACT,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;IAE7D,OAAO,GAAGG,KAAK,IAAIE,OAAO,IAAIE,OAAO,UAAUN,GAAG,IAAIJ,KAAK,IAAIF,IAAI,EAAE;EACvE;EACA,MAAMc,eAAe,GAAGA,CAACC,KAAK,EAAEnC,KAAK,KAAK;IACxCjC,eAAe,CAACoE,KAAK,CAAC;IACtBlE,cAAc,CAAC;MAAE,GAAG+B;IAAM,CAAC,CAAC;EAC9B,CAAC;EACD,IAAIoC,KAAK,GAAG,CAAC;EACb,MAAMC,YAAY,GAAIhC,KAAK,IAAK;IAC9B,MAAMiC,KAAK,GAAG,UAAU,CAAC,CAAC;IAC1B,OAAOA,KAAK,CAACC,IAAI,CAAClC,KAAK,CAAC;EAC1B,CAAC;EACD,MAAMmC,eAAe,GAAG,MAAAA,CAAA,KAAY;IAClC,IAAI,CAACH,YAAY,CAACrE,WAAW,CAACqC,KAAK,CAAC,EAAE;MACpC5C,WAAW,CAAC,CAAC;MACbb,MAAM,CAAC,CAAC,EAAE,4BAA4B,EAAE,UAAU,CAAC;MACnD;IACF;IACAY,YAAY,CAAC,CAAC;IACd,IAAIiF,GAAG,GAAG,6CAA6C;IACvD,IAAInF,QAAQ,EAAE;MACZmF,GAAG,GAAG,kDAAkD;IAC1D;IACA,MAAM5D,QAAQ,GAAG,MAAMC,KAAK,CAAC2D,GAAG,EAAE;MAChC1D,MAAM,EAAE,MAAM;MACdC,OAAO,EAAE;QACP,cAAc,EAAE;MAClB,CAAC;MACDL,IAAI,EAAEM,IAAI,CAACC,SAAS,CAAC;QACnBd,IAAI,EAAEA,IAAI;QACV,IAAId,QAAQ,GACR;UAAEoF,aAAa,EAAE1E;QAAY,CAAC,GAC9B;UAAE2E,aAAa,EAAE3E;QAAY,CAAC;MACpC,CAAC;IACH,CAAC,CAAC;IACF,MAAM4E,IAAI,GAAG,MAAM/D,QAAQ,CAACO,IAAI,CAAC,CAAC;IAClC,IAAIwD,IAAI,CAACC,OAAO,IAAI,SAAS,EAAE;MAC7B,IAAIvF,QAAQ,EAAEV,MAAM,CAAC,CAAC,EAAE,2BAA2B,EAAE,YAAY,CAAC,CAAC,KAC9D;QACHA,MAAM,CAAC,CAAC,EAAE,6BAA6B,EAAE,YAAY,CAAC;MACxD;MACA,MAAMkG,aAAa,GAAG,CAAC,GAAGpF,aAAa,CAAC;MACxCoF,aAAa,CAAChF,YAAY,CAAC,GAAGE,WAAW;MACzCL,gBAAgB,CAACmF,aAAa,CAAC;MAC/B/E,eAAe,CAAC,IAAI,CAAC,CAAC,CAAC;IACzB,CAAC,MAAM;MACLnB,MAAM,CAAC,CAAC,EAAEgG,IAAI,CAACC,OAAO,EAAE,UAAU,CAAC;IACrC;IACApF,WAAW,CAAC,CAAC;EACf,CAAC;EAED,MAAMsF,iBAAiB,GAAGA,CAAA,KAAM;IAC9BhF,eAAe,CAAC,IAAI,CAAC,CAAC,CAAC;EACzB,CAAC;EACD,MAAMiF,gBAAgB,GAAIrC,CAAC,IAAK;IAC9B,MAAM;MAAET,IAAI;MAAEW;IAAM,CAAC,GAAGF,CAAC,CAACC,MAAM;IAChC3C,cAAc,CAAEgF,SAAS,KAAM;MAAE,GAAGA,SAAS;MAAE,CAAC/C,IAAI,GAAGW;IAAM,CAAC,CAAC,CAAC;EAClE,CAAC;EACD,MAAMqC,qBAAqB,GAAIvC,CAAC,IAAK;IACnC,MAAM;MAAET,IAAI;MAAEW;IAAM,CAAC,GAAGF,CAAC,CAACC,MAAM;IAChC,IAAIC,KAAK,IAAI,EAAE,EAAE;MACf5C,cAAc,CAAEgF,SAAS,KAAM;QAAE,GAAGA,SAAS;QAAE,CAAC/C,IAAI,GAAG;MAAE,CAAC,CAAC,CAAC;MAC5D;IACF;IACA,IAAI7B,CAAC,GAAGwC,KAAK,CAACsC,OAAO,CAAC,IAAI,EAAE,EAAE,CAAC,CAACA,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC;IAClD9E,CAAC,GAAG+E,UAAU,CAAC/E,CAAC,CAAC,CAACgF,cAAc,CAAC,OAAO,CAAC;IACzCpF,cAAc,CAAEgF,SAAS,KAAM;MAAE,GAAGA,SAAS;MAAE,CAAC/C,IAAI,GAAG7B;IAAE,CAAC,CAAC,CAAC;EAC9D,CAAC;EACD,MAAMiF,aAAa,GAAG,MAAAA,CAAOhG,QAAQ,EAAEiG,MAAM,KAAK;IAChD/F,YAAY,CAAC,CAAC;IACd,IAAIiF,GAAG,GAAG,+CAA+C;IACzD,IAAInF,QAAQ,EAAE;MACZmF,GAAG,GAAG,oDAAoD;IAC5D;IACApD,OAAO,CAACC,GAAG,CAACmD,GAAG,CAAC;IAChB,MAAM5D,QAAQ,GAAG,MAAMC,KAAK,CAAC2D,GAAG,EAAE;MAChC1D,MAAM,EAAE,MAAM;MACdC,OAAO,EAAE;QACP,cAAc,EAAE;MAClB,CAAC;MACDL,IAAI,EAAEM,IAAI,CAACC,SAAS,CAAC;QACnBd,IAAI,EAAEA,IAAI;QACV,IAAId,QAAQ,GACR;UAAEkG,eAAe,EAAE9F,aAAa,CAACQ,YAAY;QAAE,CAAC,GAChD;UAAEuF,eAAe,EAAE/F,aAAa,CAACQ,YAAY;QAAE,CAAC,CAAC;QACrDwF,MAAM,EAAEH;MACV,CAAC;IACH,CAAC,CAAC;IACF,MAAMX,IAAI,GAAG,MAAM/D,QAAQ,CAACO,IAAI,CAAC,CAAC;IAClC,IAAIwD,IAAI,CAACC,OAAO,IAAI,SAAS,EAAE;MAC7B,IAAIvF,QAAQ,EAAEV,MAAM,CAAC,CAAC,EAAE,iBAAiB,EAAE,YAAY,CAAC,CAAC,KACpD;QACHA,MAAM,CAAC,CAAC,EAAE,mBAAmB,EAAE,YAAY,CAAC;MAC9C;MACA,IAAIkG,aAAa,GAAG,CAAC,GAAGpF,aAAa,CAAC;MACtCoF,aAAa,GAAGA,aAAa,CAAC/C,MAAM,CAClC,CAAC4D,IAAI,EAAExB,KAAK,KAAKA,KAAK,IAAIjE,YAC5B,CAAC;MACDP,gBAAgB,CAACmF,aAAa,CAAC;IACjC,CAAC,MAAM;MACLlG,MAAM,CAAC,CAAC,EAAEgG,IAAI,CAACC,OAAO,EAAE,UAAU,CAAC;IACrC;IACApF,WAAW,CAAC,CAAC;EACf,CAAC;EACD,oBACER,OAAA,CAAAE,SAAA;IAAAyG,QAAA,GACGnF,UAAU,iBACTxB,OAAA,CAACF,kBAAkB;MACjBO,QAAQ,EAAEA,QAAQ,GAAGmB,UAAU,GAAGoF,SAAU;MAC5CC,QAAQ,EAAExG,QAAQ,GAAGuG,SAAS,GAAGpF,UAAW;MAC5CsF,QAAQ,EAAEA,CAAA,KAAM;QACdrF,aAAa,CAAC,KAAK,CAAC;MACtB,CAAE;MACFsF,QAAQ,EAAEV;IAAc;MAAAW,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACzB,CACF,EACA7F,WAAW,iBACVtB,OAAA,CAACH,gBAAgB;MACfO,OAAO,EAAEA,CAAA,KAAM;QACbmB,cAAc,CAAC,KAAK,CAAC;MACvB,CAAE;MACFlB,QAAQ,EAAEA,QAAQ,GAAGA,QAAQ,GAAGuG,SAAU;MAC1CC,QAAQ,EAAExG,QAAQ,GAAGuG,SAAS,GAAG;IAAK;MAAAI,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACvC,CACF,EACAxG,YAAY,iBACXX,OAAA,CAACN,YAAY;MACX0H,KAAK,EAAEvD,eAAgB;MACvBrB,MAAM,EAAEA,MAAO;MACfnC,QAAQ,EAAEA;IAAS;MAAA2G,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACpB,CACF,eACDnH,OAAA;MAAKqH,SAAS,EAAC,mBAAmB;MAACC,KAAK,EAAE;QAAEC,MAAM,EAAE;MAAI,CAAE;MAAAZ,QAAA,eACxD3G,OAAA;QAAKqH,SAAS,EAAC,wBAAwB;QAAAV,QAAA,gBACrC3G,OAAA;UAAKqH,SAAS,EAAC,OAAO;UAACG,OAAO,EAAEpH,OAAQ;UAAAuG,QAAA,EAAC;QAEzC;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACNnH,OAAA;UAAKqH,SAAS,EAAC,qBAAqB;UAAAV,QAAA,gBAClC3G,OAAA;YAAIqH,SAAS,EAAC,oBAAoB;YAAAV,QAAA,EAC/B,CAACtG,QAAQ,GAAG,YAAY,GAAG;UAAc;YAAA2G,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxC,CAAC,eACLnH,OAAA;YAAKqH,SAAS,EAAC,8BAA8B;YAAAV,QAAA,gBAC3C3G,OAAA;cACEyH,IAAI,EAAC,MAAM;cACXJ,SAAS,EAAC,qBAAqB;cAC/BK,WAAW,EAAC,eAAe;cAC3B9D,KAAK,EAAEnB,UAAW;cAClBkF,QAAQ,EAAElE;YAAa;cAAAuD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxB,CAAC,eACFnH,OAAA;cACEqH,SAAS,EAAC,wBAAwB;cAClCC,KAAK,EAAE;gBAAEM,UAAU,EAAE,KAAK;gBAAEC,WAAW,EAAE;cAAO,CAAE;cAClDL,OAAO,EAAEA,CAAA,KAAM;gBACbjG,cAAc,CAAC,IAAI,CAAC;cACtB,CAAE;cAAAoF,QAAA,EACH;YAED;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTnH,OAAA;cACEqH,SAAS,EAAC,uBAAuB;cACjCC,KAAK,EAAE;gBAAEM,UAAU,EAAE,KAAK;gBAAEC,WAAW,EAAE;cAAO,CAAE;cAClDL,OAAO,EAAE1D,cAAe;cAAA6C,QAAA,EAEvB,CAACtG,QAAQ,GAAG,iBAAiB,GAAG;YAAiB;cAAA2G,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5C,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENnH,OAAA;UAAOqH,SAAS,EAAC,oBAAoB;UAAAV,QAAA,gBACnC3G,OAAA;YAAA2G,QAAA,eACE3G,OAAA;cAAA2G,QAAA,gBACE3G,OAAA;gBAAA2G,QAAA,EAAI;cAAa;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACtBnH,OAAA;gBAAA2G,QAAA,EAAI;cAAI;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACbnH,OAAA;gBAAA2G,QAAA,EAAK,CAACtG,QAAQ,GAAG,gBAAgB,GAAG;cAAkB;gBAAA2G,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAC5DnH,OAAA;gBAAA2G,QAAA,EAAI;cAAK;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,EACb,CAAC9G,QAAQ,gBACRL,OAAA,CAAAE,SAAA;gBAAAyG,QAAA,gBACE3G,OAAA;kBAAA2G,QAAA,EAAI;gBAAI;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACbnH,OAAA;kBAAA2G,QAAA,EAAI;gBAAK;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA,eACd,CAAC,gBAEHnH,OAAA,CAAAE,SAAA,mBAAI,CACL,eACDF,OAAA;gBAAA2G,QAAA,EAAI;cAAO;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACd;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC,eACRnH,OAAA;YAAA2G,QAAA,EACG9D,cAAc,CAACiF,GAAG,CAAC,CAAC/E,KAAK,EAAEmC,KAAK,kBAC/BlF,OAAA;cAAA2G,QAAA,gBACE3G,OAAA;gBAAA2G,QAAA,GACG5D,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEgF,OAAO,CAAC9E,IAAI,eACpBjD,OAAA;kBAAAgH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,KAAC,eAAAnH,OAAA;kBAAA2G,QAAA,EAAQ5D,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEgF,OAAO,CAAC5E;gBAAK;kBAAA6D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1C,CAAC,eACLnH,OAAA;gBAAA2G,QAAA,EAAK5C,cAAc,CAAChB,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEiF,SAAS;cAAC;gBAAAhB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAC3CnH,OAAA;gBAAA2G,QAAA,EACG9F,YAAY,KAAKqE,KAAK,gBACrBlF,OAAA;kBAAA2G,QAAA,gBACE3G,OAAA;oBACEyH,IAAI,EAAC,MAAM;oBACXxE,IAAI,EAAC,MAAM;oBACXW,KAAK,EAAE7C,WAAW,CAACkC,IAAK;oBACxB0E,QAAQ,EAAE5B;kBAAiB;oBAAAiB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC5B,CAAC,eACFnH,OAAA;oBACEyH,IAAI,EAAC,MAAM;oBACXxE,IAAI,EAAC,OAAO;oBACZW,KAAK,EAAE7C,WAAW,CAACoC,KAAM;oBACzBwE,QAAQ,EAAE5B;kBAAiB;oBAAAiB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC5B,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,gBAENnH,OAAA;kBAAA2G,QAAA,GACG5D,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEE,IAAI,EAAC,GAAC,eAAAjD,OAAA;oBAAAgH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,KAAC,eAAAnH,OAAA;oBAAA2G,QAAA,EAAQ5D,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEI;kBAAK;oBAAA6D,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC/C;cACN;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACLnH,OAAA;gBAAA2G,QAAA,eACE3G,OAAA;kBAAMqH,SAAS,EAAE,uBAAuBtE,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEkF,MAAM,EAAG;kBAAAtB,QAAA,EACrD9F,YAAY,KAAKqE,KAAK,gBACrBlF,OAAA;oBAAA2G,QAAA,eACE3G,OAAA;sBACEyH,IAAI,EAAC,MAAM;sBACXxE,IAAI,EAAC,OAAO;sBACZW,KAAK,EAAE7C,WAAW,CAACqC,KAAM;sBACzBuE,QAAQ,EAAE5B;oBAAiB;sBAAAiB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC5B;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC,CAAC,gBAENnH,OAAA;oBAAA2G,QAAA,EAAM5D,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEK;kBAAK;oBAAA4D,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM;gBACzB;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACG;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC,EACJ,CAAC9G,QAAQ,gBACRL,OAAA,CAAAE,SAAA;gBAAAyG,QAAA,gBACE3G,OAAA;kBAAA2G,QAAA,EACG9F,YAAY,KAAKqE,KAAK,gBACrBlF,OAAA;oBAAA2G,QAAA,eACE3G,OAAA;sBACEyH,IAAI,EAAC,MAAM;sBACXxE,IAAI,EAAC,MAAM;sBACXW,KAAK,EAAE7C,WAAW,CAACmH,IAAK;sBACxBP,QAAQ,EAAE5B;oBAAiB;sBAAAiB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC5B;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC,CAAC,gBAENnH,OAAA;oBAAA2G,QAAA,EAAM5D,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEmF;kBAAI;oBAAAlB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM;gBACxB;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,eACLnH,OAAA;kBAAA2G,QAAA,EACG9F,YAAY,KAAKqE,KAAK,gBACrBlF,OAAA;oBAAA2G,QAAA,eACE3G,OAAA;sBACEyH,IAAI,EAAC,MAAM;sBACXxE,IAAI,EAAC,OAAO;sBACZW,KAAK,EAAE7C,WAAW,CAACsC,KAAM;sBACzBsE,QAAQ,EAAE1B;oBAAsB;sBAAAe,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACjC;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC,CAAC,gBAENnH,OAAA;oBAAA2G,QAAA,EAAM5D,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEM;kBAAK;oBAAA2D,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM;gBACzB;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC;cAAA,eACL,CAAC,gBAEHnH,OAAA,CAAAE,SAAA,mBAAI,CACL,eACDF,OAAA;gBAAA2G,QAAA,EACG9F,YAAY,KAAKqE,KAAK,gBACrBlF,OAAA,CAAAE,SAAA;kBAAAyG,QAAA,gBACE3G,OAAA;oBACEqH,SAAS,EAAC,wBAAwB;oBAClCG,OAAO,EAAEjC,eAAgB;oBAAAoB,QAAA,EAC1B;kBAED;oBAAAK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACTnH,OAAA;oBACEqH,SAAS,EAAC,0BAA0B;oBACpCG,OAAO,EAAE1B,iBAAkB;oBAAAa,QAAA,EAC5B;kBAED;oBAAAK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA,eACT,CAAC,gBAEHnH,OAAA,CAAAE,SAAA;kBAAAyG,QAAA,gBACE3G,OAAA;oBACEqH,SAAS,EAAC,wBAAwB;oBAClCG,OAAO,EAAEA,CAAA,KAAMvC,eAAe,CAACC,KAAK,EAAEnC,KAAK,CAAE;oBAAA4D,QAAA,EAC9C;kBAED;oBAAAK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACTnH,OAAA;oBACEqH,SAAS,EAAC,0BAA0B;oBACpCG,OAAO,EAAEA,CAAA,KAAM;sBACb/F,aAAa,CAACsB,KAAK,CAAC;sBACpB7B,eAAe,CAACgE,KAAK,CAAC;oBACxB,CAAE;oBAAAyB,QAAA,EACH;kBAED;oBAAAK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA,eACT;cACH;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA,GAjHEjC,KAAK;cAAA8B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAkHV,CACL;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA,eACN,CAAC;AAEP,CAAC;AAAC7G,EAAA,CA/YIH,OAAO;EAAA,QAC2BV,UAAU,EAM/BD,OAAO;AAAA;AAAA2I,EAAA,GAPpBhI,OAAO;AAiZb,eAAeA,OAAO;AAAC,IAAAgI,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}