{"ast": null, "code": "var _jsxFileName = \"F:\\\\File\\\\Web\\\\BTLNMCNNPM\\\\Front\\\\NMCNPM_IT3180_20241\\\\website-app\\\\src\\\\components\\\\export\\\\main.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useRef, useEffect } from \"react\";\nimport \"./main.css\";\nimport History from \"./history\";\nimport Quagga from \"quagga\";\nimport { useLoading } from \"../introduce/Loading\";\nimport { useAuth } from \"../introduce/useAuth\";\nimport PaymentComponent from \"./thanh_toan\";\nimport CustomerInfo from \"./form_show\";\nimport { notify } from \"../../components/Notification/notification\";\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst Billing = () => {\n  _s();\n  const {\n    startLoading,\n    stopLoading\n  } = useLoading();\n  const [invoices, setInvoices] = useState([{\n    products: []\n  }]);\n  const [currentInvoice, setCurrentInvoice] = useState(0);\n  const [productCode, setProductCode] = useState(\"\");\n  const [tax, setTax] = useState(0);\n  const [taxall, setTaxAll] = useState(0);\n  const [editingIndex, setEditingIndex] = useState(null);\n  const [camera, setCamera] = useState(false);\n  const videoRef = useRef(null);\n  const streamRef = useRef(null);\n  const {\n    user,\n    loading\n  } = useAuth();\n  const [isProcessing, setIsProcessing] = useState(false);\n  const [data, setData] = useState([]);\n  const [customers, setCustomers] = useState([]);\n  const [suggestion, setSuggestion] = useState([]);\n  const [form, setForm] = useState(false);\n  const [formcustomer, setFormcustomer] = useState(false);\n  const [form_history, setForm_history] = useState(false);\n  useEffect(() => {\n    const a = async () => {\n      if (loading) {\n        return;\n      }\n      let body = {\n        user: user\n      };\n      let response = await fetch(\"http://localhost:8080/api/sell/findcode\", {\n        method: \"POST\",\n        headers: {\n          \"Content-Type\": \"application/json\"\n        },\n        body: JSON.stringify(body)\n      });\n      let datas = await response.json();\n      if (datas.message == \"success\") {\n        console.log(datas.product);\n        setData(datas.product);\n      } else {\n        notify(2, \"Load sản phẩm thất bại\", \"Thất bại\");\n      }\n      response = await fetch(\"http://localhost:8080/api/sell/getCustomer\", {\n        method: \"POST\",\n        headers: {\n          \"Content-Type\": \"application/json\"\n        },\n        body: JSON.stringify(body)\n      });\n      datas = await response.json();\n      if (datas.message == \"success\") {\n        setCustomers(datas.customers);\n      } else {\n        notify(2, \"Load sản phẩm thất bại\", \"Thất bại\");\n      }\n    };\n    a();\n  }, [loading]);\n  const addProduct = async (code = \"\") => {\n    let i = \"\";\n    if (code != \"\") {\n      i = code;\n    }\n    if (productCode != \"\") i = productCode;\n    if (i == \"\") return;\n    const updatedInvoices = [...invoices];\n    if (updatedInvoices[currentInvoice].products.some(element => element.sku == i)) {\n      updatedInvoices[currentInvoice].products.forEach(element => {\n        if (element.sku == i) {\n          element.quantity++;\n          element.total = element.quantity * parseInt(element.price.replace(/\\./g, \"\"), 10) * (1 - element.discount / 100);\n          return;\n        }\n      });\n      setInvoices(updatedInvoices);\n    } else {\n      const result = data.find(element => element.sku == i);\n      if (result) {\n        const newProduct = {\n          ...result,\n          // productCode:i,\n          quantity: 1,\n          // price:parseFloat(result.price),\n          discount: 0,\n          total: parseInt(result.price.replace(/\\./g, \"\"), 10)\n          // name:result.name\n        };\n        updatedInvoices[currentInvoice].products.push(newProduct);\n        setInvoices(updatedInvoices);\n      } else {\n        notify(2, \"Sản phẩm không tồn tại\", \"Thất bại\");\n      }\n    }\n  };\n  const adds = x => {\n    console.log(x);\n    setCustomers(a => [...a, x]);\n  };\n  const addInvoice = () => {\n    setInvoices([...invoices, {\n      products: []\n    }]);\n    setCurrentInvoice(invoices.length);\n  };\n  const removeInvoice = index => {\n    if (index == 0) {\n      return;\n    }\n    const updatedInvoices = invoices.filter((_, i) => i !== index);\n    setInvoices(updatedInvoices);\n    setCurrentInvoice(prev => prev === index ? 0 : prev - (prev > index ? 1 : 0));\n  };\n  const handleDoubleClick = index => {\n    setEditingIndex(index);\n  };\n  const handleBlur = () => {\n    setEditingIndex(null);\n  };\n  const handleChangeProduct = (index, field, value) => {\n    const updatedInvoices = [...invoices];\n    const product = updatedInvoices[currentInvoice].products[index];\n    product[field] = value;\n    product.total = product.quantity * parseInt(product.price.replace(/\\./g, \"\"), 10) * (1 - product.discount / 100);\n    setInvoices(updatedInvoices);\n  };\n  const delete_prd = index => {\n    console.log(index);\n    const updatedInvoices = [...invoices];\n    let update = invoices[currentInvoice].products;\n    update = update.filter((_, i) => i != index);\n    invoices[currentInvoice].products = update;\n    setInvoices(updatedInvoices);\n  };\n  const calculateTotal = () => {\n    return invoices[currentInvoice].products.reduce((total, product) => total + product.total, 0);\n  };\n  const deleteAllProducts = () => {\n    const updatedInvoices = [...invoices];\n    updatedInvoices[currentInvoice].products = []; // Xóa tất cả sản phẩm\n    setInvoices(updatedInvoices); // Cập nhật lại state\n  };\n  const totalBeforeTax = invoices[currentInvoice].products.reduce((acc, product) => acc + product.total, 0) * (1 - taxall / 100);\n  const totalTax = totalBeforeTax * (tax / 100);\n  const total = Math.round(totalBeforeTax + totalTax);\n  const startCamera = async () => {\n    try {\n      // Bật trạng thái camera và chuẩn bị\n      setCamera(true);\n      setIsProcessing(false);\n\n      // Yêu cầu truy cập camera\n      streamRef.current = await navigator.mediaDevices.getUserMedia({\n        video: true\n      });\n      if (videoRef.current) {\n        videoRef.current.srcObject = streamRef.current;\n      }\n\n      // Dừng Quagga nếu đã khởi tạo\n      if (Quagga.initialized) {\n        Quagga.stop();\n      }\n\n      // Khởi tạo QuaggaJS để quét mã vạch\n      if (videoRef.current) {\n        Quagga.init({\n          inputStream: {\n            name: \"Live\",\n            type: \"LiveStream\",\n            target: videoRef.current,\n            // Sử dụng video từ camera\n            constraints: {\n              facingMode: \"environment\" // Camera sau\n            },\n            willReadFrequently: true\n          },\n          decoder: {\n            readers: [\"code_128_reader\", \"ean_reader\", \"upc_reader\", \"code_39_reader\"] // Các loại mã vạch cần quét\n          }\n        }, function (err) {\n          if (err) {\n            console.error(\"Quagga init error:\", err);\n            notify(2, \"Không thể khởi động quét mã vạch. Vui lòng thử lại!\", \"Thất bại\");\n            return;\n          }\n          Quagga.initialized = true; // Đánh dấu đã khởi tạo\n          Quagga.start();\n        });\n      }\n\n      // Xử lý sự kiện khi phát hiện mã vạch\n      Quagga.offDetected(); // Xóa sự kiện trước đó\n      Quagga.onDetected(async function (result) {\n        if (isProcessing) return; // Nếu đang xử lý thì bỏ qua\n        setIsProcessing(true); // Đặt trạng thái xử lý\n\n        const code = result.codeResult.code;\n        stopCamera(); // Dừng camera sau khi quét\n        try {\n          await addProduct(code); // Gọi hàm thêm sản phẩm\n          setProductCode(code); // Lưu mã sản phẩm\n        } catch (error) {\n          console.error(\"Error in addProduct:\", error);\n        } finally {\n          setIsProcessing(false);\n        }\n      });\n    } catch (error) {\n      console.error(\"Camera error:\", error);\n      notify(2, \"Không thể mở camera. Vui lòng kiểm tra cài đặt quyền hoặc thiết bị!\", \"Thất bại\");\n    }\n  };\n  const stopCamera = () => {\n    try {\n      // Kiểm tra và dừng stream nếu có\n      if (streamRef.current) {\n        const tracks = streamRef.current.getTracks();\n        if (Array.isArray(tracks)) {\n          tracks.forEach(track => {\n            if (track && typeof track.stop === \"function\") {\n              track.stop(); // Dừng track\n            }\n          });\n        }\n        // Gỡ liên kết stream khỏi video\n        if (videoRef.current) {\n          videoRef.current.srcObject = null;\n        }\n        streamRef.current = null; // Đặt lại tham chiếu stream\n      }\n\n      // Dừng Quagga nếu đã khởi tạo\n      if (Quagga && typeof Quagga.stop === \"function\") {\n        Quagga.stop();\n        Quagga.initialized = false; // Đặt lại trạng thái Quagga\n      }\n\n      // Cập nhật trạng thái camera\n      setCamera(false);\n    } catch (error) {\n      setCamera(false);\n      console.error(\"Error stopping camera:\", error);\n      notify(2, \"Không thể dừng camera. Vui lòng thử lại!\", \"Thật bại\");\n    }\n  };\n  const onform = () => {\n    if (total > 0) {\n      setForm(true);\n    }\n  };\n  const onclose = () => {\n    setForm(false);\n  };\n  const onformcustomer = () => {\n    setFormcustomer(true);\n  };\n  const onclosecustomer = () => {\n    setFormcustomer(false);\n  };\n  const onclosehistory = () => {\n    setForm_history(false);\n  };\n  const onform_history = () => {\n    setForm_history(true);\n  };\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [form_history && /*#__PURE__*/_jsxDEV(History, {\n      turnoff: onclosehistory\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 318,\n      columnNumber: 24\n    }, this), formcustomer && /*#__PURE__*/_jsxDEV(CustomerInfo, {\n      turnoff: onclosecustomer,\n      customer: customers,\n      adds: adds,\n      supplier: false\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 320,\n      columnNumber: 9\n    }, this), form && /*#__PURE__*/_jsxDEV(PaymentComponent, {\n      close: onclose,\n      totalAmount: total,\n      products: invoices[currentInvoice].products,\n      customers: customers,\n      discount: taxall,\n      vat: tax\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 328,\n      columnNumber: 9\n    }, this), camera && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"camera-sell\",\n      children: [/*#__PURE__*/_jsxDEV(\"video\", {\n        ref: videoRef,\n        autoPlay: true,\n        width: \"400px\",\n        height: \"300px\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 339,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        className: \"button-capture-sell button-sell\",\n        onClick: stopCamera,\n        style: {\n          backgroundColor: \"red\",\n          color: \"white\"\n        },\n        children: \"H\\u1EE7y\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 341,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 338,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"billing-container\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"invoice-bar\",\n        children: [invoices.map((_, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"invoice-tab\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            className: index === currentInvoice ? \"active button-sell\" : \"button-sell\",\n            onClick: () => {\n              setCurrentInvoice(index);\n              setEditingIndex(null);\n            },\n            children: [\"H\\xF3a \\u0111\\u01A1n \", index + 1]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 354,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"button-sell\",\n            onClick: () => removeInvoice(index),\n            children: \"X\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 367,\n            columnNumber: 15\n          }, this)]\n        }, index, true, {\n          fileName: _jsxFileName,\n          lineNumber: 353,\n          columnNumber: 13\n        }, this)), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: addInvoice,\n          className: \"button-sell\",\n          children: \"Th\\xEAm H\\xF3a \\u0110\\u01A1n\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 375,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 351,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"top-bar\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-group-sell\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            className: \"label-sell\",\n            children: \"M\\xE3 s\\u1EA3n ph\\u1EA9m (F1): \"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 381,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            className: \"input-sell\",\n            type: \"text\",\n            value: productCode,\n            onChange: e => {\n              setProductCode(e.target.value);\n              if (e.target.value != \"\") {\n                const x = data.filter((product, index) => product.sku.includes(e.target.value));\n                setSuggestion(x);\n              } else {\n                setSuggestion([]);\n              }\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 382,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n            id: \"suggestions-sell\",\n            children: suggestion.map((product, index) => {\n              return /*#__PURE__*/_jsxDEV(\"li\", {\n                onClick: () => {\n                  setProductCode(product.sku);\n                  setSuggestion([]);\n                },\n                children: product.sku\n              }, index, false, {\n                fileName: _jsxFileName,\n                lineNumber: 401,\n                columnNumber: 19\n              }, this);\n            })\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 398,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            style: {\n              marginTop: \"10px\",\n              color: \"white\"\n            },\n            onClick: startCamera,\n            className: \"button-sell\",\n            children: \"Qu\\xE9t m\\xE3\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 413,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => addProduct(),\n            style: {\n              color: \"white\",\n              marginLeft: \"10px\"\n            },\n            className: \"button-sell\",\n            children: \"Th\\xEAm s\\u1EA3n ph\\u1EA9m\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 420,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 380,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"xx\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"history\",\n            onClick: onform_history,\n            children: \"L\\u1ECBch s\\u1EED\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 429,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 432,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"create_user\",\n            onClick: onformcustomer,\n            children: \"Danh s\\xE1ch kh\\xE1ch h\\xE0ng\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 433,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 428,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 379,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"product-list\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          children: \"Danh s\\xE1ch s\\u1EA3n ph\\u1EA9m\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 439,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"table\", {\n          children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n            children: /*#__PURE__*/_jsxDEV(\"tr\", {\n              children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"Barcode\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 443,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"T\\xEAn s\\u1EA3n ph\\u1EA9m\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 444,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"S\\u1ED1 l\\u01B0\\u1EE3ng\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 445,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"Gi\\xE1 b\\xE1n\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 446,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"Gi\\u1EA3m gi\\xE1 (%)\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 447,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"Th\\xE0nh ti\\u1EC1n\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 448,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 442,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 441,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n            children: [invoices[currentInvoice].products.map((product, index) => /*#__PURE__*/_jsxDEV(\"tr\", {\n              onDoubleClick: () => handleDoubleClick(index),\n              children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                children: product.sku\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 454,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                children: product.name\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 455,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                children: editingIndex === index ? /*#__PURE__*/_jsxDEV(\"input\", {\n                  className: \"input-sell\",\n                  type: \"number\",\n                  value: product.quantity,\n                  onChange: e => handleChangeProduct(index, \"quantity\", Number(e.target.value)),\n                  onBlur: handleBlur\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 458,\n                  columnNumber: 23\n                }, this) : product.quantity\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 456,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                children: product.price\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 475,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                children: editingIndex === index ? /*#__PURE__*/_jsxDEV(\"input\", {\n                  className: \"input-sell\",\n                  type: \"number\",\n                  value: product.discount,\n                  onChange: e => handleChangeProduct(index, \"discount\", Number(e.target.value)),\n                  onBlur: handleBlur\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 478,\n                  columnNumber: 23\n                }, this) : product.discount\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 476,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                children: product.total.toLocaleString(\"vi-VN\")\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 495,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"delete_prd\",\n                onClick: () => {\n                  delete_prd(index);\n                },\n                children: \"x\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 496,\n                columnNumber: 19\n              }, this)]\n            }, index, true, {\n              fileName: _jsxFileName,\n              lineNumber: 453,\n              columnNumber: 17\n            }, this)), /*#__PURE__*/_jsxDEV(\"tr\", {\n              children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                colSpan: 5,\n                style: {\n                  textAlign: \"right\"\n                },\n                children: /*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"T\\u1ED5ng c\\u1ED9ng:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 508,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 507,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                style: {\n                  textAlign: \"right\"\n                },\n                children: calculateTotal().toLocaleString(\"vi-VN\")\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 510,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"delete_prd\",\n                onClick: deleteAllProducts,\n                children: \"X\\xF3a h\\u1EBFt\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 513,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 506,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 451,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 440,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 438,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"summary\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-group-sell\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            className: \"label-sell\",\n            children: \"Thu\\u1EBF su\\u1EA5t (%): \"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 522,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            className: \"input-sell\",\n            type: \"number\",\n            value: tax,\n            onChange: e => setTax(Number(e.target.value))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 523,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n            className: \"label-sell\",\n            style: {\n              marginTop: \"10px\"\n            },\n            children: [\"Gi\\u1EA3m gi\\xE1 cho to\\xE0n b\\u1ED9 s\\u1EA3n ph\\u1EA9m (%):\", \" \"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 529,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            className: \"input-sell\",\n            type: \"number\",\n            value: taxall,\n            onChange: e => setTaxAll(Number(e.target.value))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 532,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 521,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"result\",\n          children: /*#__PURE__*/_jsxDEV(\"h2\", {\n            style: {\n              marginTop: \"10px\"\n            },\n            children: [\"T\\u1ED5ng h\\xF3a \\u0111\\u01A1n: \", total.toLocaleString(\"vi-VN\")]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 540,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 539,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"button-sell\",\n          style: {\n            color: \"white\",\n            marginTop: \"10px\"\n          },\n          onClick: onform,\n          children: \"Thanh to\\xE1n\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 544,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 520,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 350,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true);\n};\n_s(Billing, \"OnL4QKW/ra+85Q8U0X+1M6MFZFQ=\", false, function () {\n  return [useLoading, useAuth];\n});\n_c = Billing;\nexport default Billing;\nvar _c;\n$RefreshReg$(_c, \"Billing\");", "map": {"version": 3, "names": ["React", "useState", "useRef", "useEffect", "History", "Quagga", "useLoading", "useAuth", "PaymentComponent", "CustomerInfo", "notify", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "Billing", "_s", "startLoading", "stopLoading", "invoices", "setInvoices", "products", "currentInvoice", "setCurrentInvoice", "productCode", "setProductCode", "tax", "setTax", "taxall", "setTaxAll", "editingIndex", "setEditingIndex", "camera", "setCamera", "videoRef", "streamRef", "user", "loading", "isProcessing", "setIsProcessing", "data", "setData", "customers", "setCustomers", "suggestion", "setSuggestion", "form", "setForm", "<PERSON><PERSON><PERSON><PERSON>", "set<PERSON><PERSON><PERSON><PERSON><PERSON>", "form_history", "setForm_history", "a", "body", "response", "fetch", "method", "headers", "JSON", "stringify", "datas", "json", "message", "console", "log", "product", "addProduct", "code", "i", "updatedInvoices", "some", "element", "sku", "for<PERSON>ach", "quantity", "total", "parseInt", "price", "replace", "discount", "result", "find", "newProduct", "push", "adds", "x", "addInvoice", "length", "removeInvoice", "index", "filter", "_", "prev", "handleDoubleClick", "handleBlur", "handleChangeProduct", "field", "value", "delete_prd", "update", "calculateTotal", "reduce", "deleteAllProducts", "totalBeforeTax", "acc", "totalTax", "Math", "round", "startCamera", "current", "navigator", "mediaDevices", "getUserMedia", "video", "srcObject", "initialized", "stop", "init", "inputStream", "name", "type", "target", "constraints", "facingMode", "willReadFrequently", "decoder", "readers", "err", "error", "start", "offDetected", "onDetected", "codeResult", "stopCamera", "tracks", "getTracks", "Array", "isArray", "track", "onform", "onclose", "onformcus<PERSON><PERSON>", "onclosecus<PERSON><PERSON>", "onclosehistory", "onform_history", "children", "turnoff", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "customer", "supplier", "close", "totalAmount", "vat", "className", "ref", "autoPlay", "width", "height", "onClick", "style", "backgroundColor", "color", "map", "onChange", "e", "includes", "id", "marginTop", "marginLeft", "onDoubleClick", "Number", "onBlur", "toLocaleString", "colSpan", "textAlign", "_c", "$RefreshReg$"], "sources": ["F:/File/Web/BTLNMCNNPM/Front/NMCNPM_IT3180_20241/website-app/src/components/export/main.js"], "sourcesContent": ["import React, { useState, useRef, useEffect } from \"react\";\r\nimport \"./main.css\";\r\nimport History from \"./history\";\r\nimport Quagga from \"quagga\";\r\nimport { useLoading } from \"../introduce/Loading\";\r\nimport { useAuth } from \"../introduce/useAuth\";\r\nimport PaymentComponent from \"./thanh_toan\";\r\nimport CustomerInfo from \"./form_show\";\r\nimport { notify } from \"../../components/Notification/notification\";\r\nconst Billing = () => {\r\n  const { startLoading, stopLoading } = useLoading();\r\n  const [invoices, setInvoices] = useState([{ products: [] }]);\r\n  const [currentInvoice, setCurrentInvoice] = useState(0);\r\n  const [productCode, setProductCode] = useState(\"\");\r\n  const [tax, setTax] = useState(0);\r\n  const [taxall, setTaxAll] = useState(0);\r\n  const [editingIndex, setEditingIndex] = useState(null);\r\n  const [camera, setCamera] = useState(false);\r\n  const videoRef = useRef(null);\r\n  const streamRef = useRef(null);\r\n  const { user, loading } = useAuth();\r\n  const [isProcessing, setIsProcessing] = useState(false);\r\n  const [data, setData] = useState([]);\r\n  const [customers, setCustomers] = useState([]);\r\n  const [suggestion, setSuggestion] = useState([]);\r\n  const [form, setForm] = useState(false);\r\n  const [formcustomer, setFormcustomer] = useState(false);\r\n  const [form_history, setForm_history] = useState(false);\r\n  useEffect(() => {\r\n    const a = async () => {\r\n      if (loading) {\r\n        return;\r\n      }\r\n      let body = {\r\n        user: user,\r\n      };\r\n      let response = await fetch(\"http://localhost:8080/api/sell/findcode\", {\r\n        method: \"POST\",\r\n        headers: {\r\n          \"Content-Type\": \"application/json\",\r\n        },\r\n        body: JSON.stringify(body),\r\n      });\r\n      let datas = await response.json();\r\n      if (datas.message == \"success\") {\r\n        console.log(datas.product);\r\n        setData(datas.product);\r\n      } else {\r\n        notify(2, \"Load sản phẩm thất bại\", \"Thất bại\");\r\n      }\r\n      response = await fetch(\"http://localhost:8080/api/sell/getCustomer\", {\r\n        method: \"POST\",\r\n        headers: {\r\n          \"Content-Type\": \"application/json\",\r\n        },\r\n        body: JSON.stringify(body),\r\n      });\r\n      datas = await response.json();\r\n      if (datas.message == \"success\") {\r\n        setCustomers(datas.customers);\r\n      } else {\r\n        notify(2, \"Load sản phẩm thất bại\", \"Thất bại\");\r\n      }\r\n    };\r\n    a();\r\n  }, [loading]);\r\n  const addProduct = async (code = \"\") => {\r\n    let i = \"\";\r\n    if (code != \"\") {\r\n      i = code;\r\n    }\r\n    if (productCode != \"\") i = productCode;\r\n    if (i == \"\") return;\r\n    const updatedInvoices = [...invoices];\r\n    if (\r\n      updatedInvoices[currentInvoice].products.some(\r\n        (element) => element.sku == i\r\n      )\r\n    ) {\r\n      updatedInvoices[currentInvoice].products.forEach((element) => {\r\n        if (element.sku == i) {\r\n          element.quantity++;\r\n          element.total =\r\n            element.quantity *\r\n            parseInt(element.price.replace(/\\./g, \"\"), 10) *\r\n            (1 - element.discount / 100);\r\n          return;\r\n        }\r\n      });\r\n\r\n      setInvoices(updatedInvoices);\r\n    } else {\r\n      const result = data.find((element) => element.sku == i);\r\n      if (result) {\r\n        const newProduct = {\r\n          ...result,\r\n          // productCode:i,\r\n          quantity: 1,\r\n          // price:parseFloat(result.price),\r\n          discount: 0,\r\n          total: parseInt(result.price.replace(/\\./g, \"\"), 10),\r\n          // name:result.name\r\n        };\r\n        updatedInvoices[currentInvoice].products.push(newProduct);\r\n        setInvoices(updatedInvoices);\r\n      } else {\r\n        notify(2, \"Sản phẩm không tồn tại\", \"Thất bại\");\r\n      }\r\n    }\r\n  };\r\n  const adds = (x) => {\r\n    console.log(x);\r\n    setCustomers((a) => [...a, x]);\r\n  };\r\n  const addInvoice = () => {\r\n    setInvoices([...invoices, { products: [] }]);\r\n    setCurrentInvoice(invoices.length);\r\n  };\r\n\r\n  const removeInvoice = (index) => {\r\n    if (index == 0) {\r\n      return;\r\n    }\r\n    const updatedInvoices = invoices.filter((_, i) => i !== index);\r\n    setInvoices(updatedInvoices);\r\n    setCurrentInvoice((prev) =>\r\n      prev === index ? 0 : prev - (prev > index ? 1 : 0)\r\n    );\r\n  };\r\n\r\n  const handleDoubleClick = (index) => {\r\n    setEditingIndex(index);\r\n  };\r\n\r\n  const handleBlur = () => {\r\n    setEditingIndex(null);\r\n  };\r\n\r\n  const handleChangeProduct = (index, field, value) => {\r\n    const updatedInvoices = [...invoices];\r\n    const product = updatedInvoices[currentInvoice].products[index];\r\n\r\n    product[field] = value;\r\n    product.total =\r\n      product.quantity *\r\n      parseInt(product.price.replace(/\\./g, \"\"), 10) *\r\n      (1 - product.discount / 100);\r\n    setInvoices(updatedInvoices);\r\n  };\r\n  const delete_prd = (index) => {\r\n    console.log(index);\r\n    const updatedInvoices = [...invoices];\r\n    let update = invoices[currentInvoice].products;\r\n    update = update.filter((_, i) => i != index);\r\n    invoices[currentInvoice].products = update;\r\n    setInvoices(updatedInvoices);\r\n  };\r\n  const calculateTotal = () => {\r\n    return invoices[currentInvoice].products.reduce(\r\n      (total, product) => total + product.total,\r\n      0\r\n    );\r\n  };\r\n  const deleteAllProducts = () => {\r\n    const updatedInvoices = [...invoices];\r\n    updatedInvoices[currentInvoice].products = []; // Xóa tất cả sản phẩm\r\n    setInvoices(updatedInvoices); // Cập nhật lại state\r\n  };\r\n  const totalBeforeTax =\r\n    invoices[currentInvoice].products.reduce(\r\n      (acc, product) => acc + product.total,\r\n      0\r\n    ) *\r\n    (1 - taxall / 100);\r\n\r\n  const totalTax = totalBeforeTax * (tax / 100);\r\n  const total = Math.round(totalBeforeTax + totalTax);\r\n  const startCamera = async () => {\r\n    try {\r\n      // Bật trạng thái camera và chuẩn bị\r\n      setCamera(true);\r\n      setIsProcessing(false);\r\n\r\n      // Yêu cầu truy cập camera\r\n      streamRef.current = await navigator.mediaDevices.getUserMedia({\r\n        video: true,\r\n      });\r\n      if (videoRef.current) {\r\n        videoRef.current.srcObject = streamRef.current;\r\n      }\r\n\r\n      // Dừng Quagga nếu đã khởi tạo\r\n      if (Quagga.initialized) {\r\n        Quagga.stop();\r\n      }\r\n\r\n      // Khởi tạo QuaggaJS để quét mã vạch\r\n      if (videoRef.current) {\r\n        Quagga.init(\r\n          {\r\n            inputStream: {\r\n              name: \"Live\",\r\n              type: \"LiveStream\",\r\n              target: videoRef.current, // Sử dụng video từ camera\r\n              constraints: {\r\n                facingMode: \"environment\", // Camera sau\r\n              },\r\n              willReadFrequently: true,\r\n            },\r\n            decoder: {\r\n              readers: [\r\n                \"code_128_reader\",\r\n                \"ean_reader\",\r\n                \"upc_reader\",\r\n                \"code_39_reader\",\r\n              ], // Các loại mã vạch cần quét\r\n            },\r\n          },\r\n          function (err) {\r\n            if (err) {\r\n              console.error(\"Quagga init error:\", err);\r\n              notify(\r\n                2,\r\n                \"Không thể khởi động quét mã vạch. Vui lòng thử lại!\",\r\n                \"Thất bại\"\r\n              );\r\n              return;\r\n            }\r\n            Quagga.initialized = true; // Đánh dấu đã khởi tạo\r\n            Quagga.start();\r\n          }\r\n        );\r\n      }\r\n\r\n      // Xử lý sự kiện khi phát hiện mã vạch\r\n      Quagga.offDetected(); // Xóa sự kiện trước đó\r\n      Quagga.onDetected(async function (result) {\r\n        if (isProcessing) return; // Nếu đang xử lý thì bỏ qua\r\n        setIsProcessing(true); // Đặt trạng thái xử lý\r\n\r\n        const code = result.codeResult.code;\r\n        stopCamera(); // Dừng camera sau khi quét\r\n        try {\r\n          await addProduct(code); // Gọi hàm thêm sản phẩm\r\n          setProductCode(code); // Lưu mã sản phẩm\r\n        } catch (error) {\r\n          console.error(\"Error in addProduct:\", error);\r\n        } finally {\r\n          setIsProcessing(false);\r\n        }\r\n      });\r\n    } catch (error) {\r\n      console.error(\"Camera error:\", error);\r\n      notify(\r\n        2,\r\n        \"Không thể mở camera. Vui lòng kiểm tra cài đặt quyền hoặc thiết bị!\",\r\n        \"Thất bại\"\r\n      );\r\n    }\r\n  };\r\n\r\n  const stopCamera = () => {\r\n    try {\r\n      // Kiểm tra và dừng stream nếu có\r\n      if (streamRef.current) {\r\n        const tracks = streamRef.current.getTracks();\r\n        if (Array.isArray(tracks)) {\r\n          tracks.forEach((track) => {\r\n            if (track && typeof track.stop === \"function\") {\r\n              track.stop(); // Dừng track\r\n            }\r\n          });\r\n        }\r\n        // Gỡ liên kết stream khỏi video\r\n        if (videoRef.current) {\r\n          videoRef.current.srcObject = null;\r\n        }\r\n        streamRef.current = null; // Đặt lại tham chiếu stream\r\n      }\r\n\r\n      // Dừng Quagga nếu đã khởi tạo\r\n      if (Quagga && typeof Quagga.stop === \"function\") {\r\n        Quagga.stop();\r\n        Quagga.initialized = false; // Đặt lại trạng thái Quagga\r\n      }\r\n\r\n      // Cập nhật trạng thái camera\r\n      setCamera(false);\r\n    } catch (error) {\r\n      setCamera(false);\r\n      console.error(\"Error stopping camera:\", error);\r\n      notify(2, \"Không thể dừng camera. Vui lòng thử lại!\", \"Thật bại\");\r\n    }\r\n  };\r\n\r\n  const onform = () => {\r\n    if (total > 0) {\r\n      setForm(true);\r\n    }\r\n  };\r\n  const onclose = () => {\r\n    setForm(false);\r\n  };\r\n  const onformcustomer = () => {\r\n    setFormcustomer(true);\r\n  };\r\n  const onclosecustomer = () => {\r\n    setFormcustomer(false);\r\n  };\r\n  const onclosehistory = () => {\r\n    setForm_history(false);\r\n  };\r\n  const onform_history = () => {\r\n    setForm_history(true);\r\n  };\r\n  return (\r\n    <>\r\n      {form_history && <History turnoff={onclosehistory} />}\r\n      {formcustomer && (\r\n        <CustomerInfo\r\n          turnoff={onclosecustomer}\r\n          customer={customers}\r\n          adds={adds}\r\n          supplier={false}\r\n        />\r\n      )}\r\n      {form && (\r\n        <PaymentComponent\r\n          close={onclose}\r\n          totalAmount={total}\r\n          products={invoices[currentInvoice].products}\r\n          customers={customers}\r\n          discount={taxall}\r\n          vat={tax}\r\n        />\r\n      )}\r\n      {camera && (\r\n        <div className=\"camera-sell\">\r\n          <video ref={videoRef} autoPlay width=\"400px\" height=\"300px\" />\r\n\r\n          <button\r\n            className=\"button-capture-sell button-sell\"\r\n            onClick={stopCamera}\r\n            style={{ backgroundColor: \"red\", color: \"white\" }}\r\n          >\r\n            Hủy\r\n          </button>\r\n        </div>\r\n      )}\r\n      <div className=\"billing-container\">\r\n        <div className=\"invoice-bar\">\r\n          {invoices.map((_, index) => (\r\n            <div key={index} className=\"invoice-tab\">\r\n              <button\r\n                className={\r\n                  index === currentInvoice\r\n                    ? \"active button-sell\"\r\n                    : \"button-sell\"\r\n                }\r\n                onClick={() => {\r\n                  setCurrentInvoice(index);\r\n                  setEditingIndex(null);\r\n                }}\r\n              >\r\n                Hóa đơn {index + 1}\r\n              </button>\r\n              <button\r\n                className=\"button-sell\"\r\n                onClick={() => removeInvoice(index)}\r\n              >\r\n                X\r\n              </button>\r\n            </div>\r\n          ))}\r\n          <button onClick={addInvoice} className=\"button-sell\">\r\n            Thêm Hóa Đơn\r\n          </button>\r\n        </div>\r\n        <div className=\"top-bar\">\r\n          <div className=\"form-group-sell\">\r\n            <label className=\"label-sell\">Mã sản phẩm (F1): </label>\r\n            <input\r\n              className=\"input-sell\"\r\n              type=\"text\"\r\n              value={productCode}\r\n              onChange={(e) => {\r\n                setProductCode(e.target.value);\r\n                if (e.target.value != \"\") {\r\n                  const x = data.filter((product, index) =>\r\n                    product.sku.includes(e.target.value)\r\n                  );\r\n                  setSuggestion(x);\r\n                } else {\r\n                  setSuggestion([]);\r\n                }\r\n              }}\r\n            />\r\n            <ul id=\"suggestions-sell\">\r\n              {suggestion.map((product, index) => {\r\n                return (\r\n                  <li\r\n                    key={index}\r\n                    onClick={() => {\r\n                      setProductCode(product.sku);\r\n                      setSuggestion([]);\r\n                    }}\r\n                  >\r\n                    {product.sku}\r\n                  </li>\r\n                );\r\n              })}\r\n            </ul>\r\n            <button\r\n              style={{ marginTop: \"10px\", color: \"white\" }}\r\n              onClick={startCamera}\r\n              className=\"button-sell\"\r\n            >\r\n              Quét mã\r\n            </button>\r\n            <button\r\n              onClick={() => addProduct()}\r\n              style={{ color: \"white\", marginLeft: \"10px\" }}\r\n              className=\"button-sell\"\r\n            >\r\n              Thêm sản phẩm\r\n            </button>\r\n          </div>\r\n          <div className=\"xx\">\r\n            <button className=\"history\" onClick={onform_history}>\r\n              Lịch sử\r\n            </button>\r\n            <br />\r\n            <button className=\"create_user\" onClick={onformcustomer}>\r\n              Danh sách khách hàng\r\n            </button>\r\n          </div>\r\n        </div>\r\n        <div className=\"product-list\">\r\n          <h2>Danh sách sản phẩm</h2>\r\n          <table>\r\n            <thead>\r\n              <tr>\r\n                <th>Barcode</th>\r\n                <th>Tên sản phẩm</th>\r\n                <th>Số lượng</th>\r\n                <th>Giá bán</th>\r\n                <th>Giảm giá (%)</th>\r\n                <th>Thành tiền</th>\r\n              </tr>\r\n            </thead>\r\n            <tbody>\r\n              {invoices[currentInvoice].products.map((product, index) => (\r\n                <tr key={index} onDoubleClick={() => handleDoubleClick(index)}>\r\n                  <td>{product.sku}</td>\r\n                  <td>{product.name}</td>\r\n                  <td>\r\n                    {editingIndex === index ? (\r\n                      <input\r\n                        className=\"input-sell\"\r\n                        type=\"number\"\r\n                        value={product.quantity}\r\n                        onChange={(e) =>\r\n                          handleChangeProduct(\r\n                            index,\r\n                            \"quantity\",\r\n                            Number(e.target.value)\r\n                          )\r\n                        }\r\n                        onBlur={handleBlur}\r\n                      />\r\n                    ) : (\r\n                      product.quantity\r\n                    )}\r\n                  </td>\r\n                  <td>{product.price}</td>\r\n                  <td>\r\n                    {editingIndex === index ? (\r\n                      <input\r\n                        className=\"input-sell\"\r\n                        type=\"number\"\r\n                        value={product.discount}\r\n                        onChange={(e) =>\r\n                          handleChangeProduct(\r\n                            index,\r\n                            \"discount\",\r\n                            Number(e.target.value)\r\n                          )\r\n                        }\r\n                        onBlur={handleBlur}\r\n                      />\r\n                    ) : (\r\n                      product.discount\r\n                    )}\r\n                  </td>\r\n                  <td>{product.total.toLocaleString(\"vi-VN\")}</td>\r\n                  <td\r\n                    className=\"delete_prd\"\r\n                    onClick={() => {\r\n                      delete_prd(index);\r\n                    }}\r\n                  >\r\n                    x\r\n                  </td>\r\n                </tr>\r\n              ))}\r\n              <tr>\r\n                <td colSpan={5} style={{ textAlign: \"right\" }}>\r\n                  <strong>Tổng cộng:</strong>\r\n                </td>\r\n                <td style={{ textAlign: \"right\" }}>\r\n                  {calculateTotal().toLocaleString(\"vi-VN\")}\r\n                </td>\r\n                <td className=\"delete_prd\" onClick={deleteAllProducts}>\r\n                  Xóa hết\r\n                </td>\r\n              </tr>\r\n            </tbody>\r\n          </table>\r\n        </div>\r\n        <div className=\"summary\">\r\n          <div className=\"form-group-sell\">\r\n            <label className=\"label-sell\">Thuế suất (%): </label>\r\n            <input\r\n              className=\"input-sell\"\r\n              type=\"number\"\r\n              value={tax}\r\n              onChange={(e) => setTax(Number(e.target.value))}\r\n            />\r\n            <label className=\"label-sell\" style={{ marginTop: \"10px\" }}>\r\n              Giảm giá cho toàn bộ sản phẩm (%):{\" \"}\r\n            </label>\r\n            <input\r\n              className=\"input-sell\"\r\n              type=\"number\"\r\n              value={taxall}\r\n              onChange={(e) => setTaxAll(Number(e.target.value))}\r\n            />\r\n          </div>\r\n          <div className=\"result\">\r\n            <h2 style={{ marginTop: \"10px\" }}>\r\n              Tổng hóa đơn: {total.toLocaleString(\"vi-VN\")}\r\n            </h2>\r\n          </div>\r\n          <button\r\n            className=\"button-sell\"\r\n            style={{ color: \"white\", marginTop: \"10px\" }}\r\n            onClick={onform}\r\n          >\r\n            Thanh toán\r\n          </button>\r\n        </div>\r\n      </div>\r\n    </>\r\n  );\r\n};\r\n\r\nexport default Billing;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,MAAM,EAAEC,SAAS,QAAQ,OAAO;AAC1D,OAAO,YAAY;AACnB,OAAOC,OAAO,MAAM,WAAW;AAC/B,OAAOC,MAAM,MAAM,QAAQ;AAC3B,SAASC,UAAU,QAAQ,sBAAsB;AACjD,SAASC,OAAO,QAAQ,sBAAsB;AAC9C,OAAOC,gBAAgB,MAAM,cAAc;AAC3C,OAAOC,YAAY,MAAM,aAAa;AACtC,SAASC,MAAM,QAAQ,4CAA4C;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AACpE,MAAMC,OAAO,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACpB,MAAM;IAAEC,YAAY;IAAEC;EAAY,CAAC,GAAGZ,UAAU,CAAC,CAAC;EAClD,MAAM,CAACa,QAAQ,EAAEC,WAAW,CAAC,GAAGnB,QAAQ,CAAC,CAAC;IAAEoB,QAAQ,EAAE;EAAG,CAAC,CAAC,CAAC;EAC5D,MAAM,CAACC,cAAc,EAAEC,iBAAiB,CAAC,GAAGtB,QAAQ,CAAC,CAAC,CAAC;EACvD,MAAM,CAACuB,WAAW,EAAEC,cAAc,CAAC,GAAGxB,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACyB,GAAG,EAAEC,MAAM,CAAC,GAAG1B,QAAQ,CAAC,CAAC,CAAC;EACjC,MAAM,CAAC2B,MAAM,EAAEC,SAAS,CAAC,GAAG5B,QAAQ,CAAC,CAAC,CAAC;EACvC,MAAM,CAAC6B,YAAY,EAAEC,eAAe,CAAC,GAAG9B,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAAC+B,MAAM,EAAEC,SAAS,CAAC,GAAGhC,QAAQ,CAAC,KAAK,CAAC;EAC3C,MAAMiC,QAAQ,GAAGhC,MAAM,CAAC,IAAI,CAAC;EAC7B,MAAMiC,SAAS,GAAGjC,MAAM,CAAC,IAAI,CAAC;EAC9B,MAAM;IAAEkC,IAAI;IAAEC;EAAQ,CAAC,GAAG9B,OAAO,CAAC,CAAC;EACnC,MAAM,CAAC+B,YAAY,EAAEC,eAAe,CAAC,GAAGtC,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACuC,IAAI,EAAEC,OAAO,CAAC,GAAGxC,QAAQ,CAAC,EAAE,CAAC;EACpC,MAAM,CAACyC,SAAS,EAAEC,YAAY,CAAC,GAAG1C,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAAC2C,UAAU,EAAEC,aAAa,CAAC,GAAG5C,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAAC6C,IAAI,EAAEC,OAAO,CAAC,GAAG9C,QAAQ,CAAC,KAAK,CAAC;EACvC,MAAM,CAAC+C,YAAY,EAAEC,eAAe,CAAC,GAAGhD,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACiD,YAAY,EAAEC,eAAe,CAAC,GAAGlD,QAAQ,CAAC,KAAK,CAAC;EACvDE,SAAS,CAAC,MAAM;IACd,MAAMiD,CAAC,GAAG,MAAAA,CAAA,KAAY;MACpB,IAAIf,OAAO,EAAE;QACX;MACF;MACA,IAAIgB,IAAI,GAAG;QACTjB,IAAI,EAAEA;MACR,CAAC;MACD,IAAIkB,QAAQ,GAAG,MAAMC,KAAK,CAAC,yCAAyC,EAAE;QACpEC,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE;UACP,cAAc,EAAE;QAClB,CAAC;QACDJ,IAAI,EAAEK,IAAI,CAACC,SAAS,CAACN,IAAI;MAC3B,CAAC,CAAC;MACF,IAAIO,KAAK,GAAG,MAAMN,QAAQ,CAACO,IAAI,CAAC,CAAC;MACjC,IAAID,KAAK,CAACE,OAAO,IAAI,SAAS,EAAE;QAC9BC,OAAO,CAACC,GAAG,CAACJ,KAAK,CAACK,OAAO,CAAC;QAC1BxB,OAAO,CAACmB,KAAK,CAACK,OAAO,CAAC;MACxB,CAAC,MAAM;QACLvD,MAAM,CAAC,CAAC,EAAE,wBAAwB,EAAE,UAAU,CAAC;MACjD;MACA4C,QAAQ,GAAG,MAAMC,KAAK,CAAC,4CAA4C,EAAE;QACnEC,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE;UACP,cAAc,EAAE;QAClB,CAAC;QACDJ,IAAI,EAAEK,IAAI,CAACC,SAAS,CAACN,IAAI;MAC3B,CAAC,CAAC;MACFO,KAAK,GAAG,MAAMN,QAAQ,CAACO,IAAI,CAAC,CAAC;MAC7B,IAAID,KAAK,CAACE,OAAO,IAAI,SAAS,EAAE;QAC9BnB,YAAY,CAACiB,KAAK,CAAClB,SAAS,CAAC;MAC/B,CAAC,MAAM;QACLhC,MAAM,CAAC,CAAC,EAAE,wBAAwB,EAAE,UAAU,CAAC;MACjD;IACF,CAAC;IACD0C,CAAC,CAAC,CAAC;EACL,CAAC,EAAE,CAACf,OAAO,CAAC,CAAC;EACb,MAAM6B,UAAU,GAAG,MAAAA,CAAOC,IAAI,GAAG,EAAE,KAAK;IACtC,IAAIC,CAAC,GAAG,EAAE;IACV,IAAID,IAAI,IAAI,EAAE,EAAE;MACdC,CAAC,GAAGD,IAAI;IACV;IACA,IAAI3C,WAAW,IAAI,EAAE,EAAE4C,CAAC,GAAG5C,WAAW;IACtC,IAAI4C,CAAC,IAAI,EAAE,EAAE;IACb,MAAMC,eAAe,GAAG,CAAC,GAAGlD,QAAQ,CAAC;IACrC,IACEkD,eAAe,CAAC/C,cAAc,CAAC,CAACD,QAAQ,CAACiD,IAAI,CAC1CC,OAAO,IAAKA,OAAO,CAACC,GAAG,IAAIJ,CAC9B,CAAC,EACD;MACAC,eAAe,CAAC/C,cAAc,CAAC,CAACD,QAAQ,CAACoD,OAAO,CAAEF,OAAO,IAAK;QAC5D,IAAIA,OAAO,CAACC,GAAG,IAAIJ,CAAC,EAAE;UACpBG,OAAO,CAACG,QAAQ,EAAE;UAClBH,OAAO,CAACI,KAAK,GACXJ,OAAO,CAACG,QAAQ,GAChBE,QAAQ,CAACL,OAAO,CAACM,KAAK,CAACC,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC,IAC7C,CAAC,GAAGP,OAAO,CAACQ,QAAQ,GAAG,GAAG,CAAC;UAC9B;QACF;MACF,CAAC,CAAC;MAEF3D,WAAW,CAACiD,eAAe,CAAC;IAC9B,CAAC,MAAM;MACL,MAAMW,MAAM,GAAGxC,IAAI,CAACyC,IAAI,CAAEV,OAAO,IAAKA,OAAO,CAACC,GAAG,IAAIJ,CAAC,CAAC;MACvD,IAAIY,MAAM,EAAE;QACV,MAAME,UAAU,GAAG;UACjB,GAAGF,MAAM;UACT;UACAN,QAAQ,EAAE,CAAC;UACX;UACAK,QAAQ,EAAE,CAAC;UACXJ,KAAK,EAAEC,QAAQ,CAACI,MAAM,CAACH,KAAK,CAACC,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,EAAE,EAAE;UACnD;QACF,CAAC;QACDT,eAAe,CAAC/C,cAAc,CAAC,CAACD,QAAQ,CAAC8D,IAAI,CAACD,UAAU,CAAC;QACzD9D,WAAW,CAACiD,eAAe,CAAC;MAC9B,CAAC,MAAM;QACL3D,MAAM,CAAC,CAAC,EAAE,wBAAwB,EAAE,UAAU,CAAC;MACjD;IACF;EACF,CAAC;EACD,MAAM0E,IAAI,GAAIC,CAAC,IAAK;IAClBtB,OAAO,CAACC,GAAG,CAACqB,CAAC,CAAC;IACd1C,YAAY,CAAES,CAAC,IAAK,CAAC,GAAGA,CAAC,EAAEiC,CAAC,CAAC,CAAC;EAChC,CAAC;EACD,MAAMC,UAAU,GAAGA,CAAA,KAAM;IACvBlE,WAAW,CAAC,CAAC,GAAGD,QAAQ,EAAE;MAAEE,QAAQ,EAAE;IAAG,CAAC,CAAC,CAAC;IAC5CE,iBAAiB,CAACJ,QAAQ,CAACoE,MAAM,CAAC;EACpC,CAAC;EAED,MAAMC,aAAa,GAAIC,KAAK,IAAK;IAC/B,IAAIA,KAAK,IAAI,CAAC,EAAE;MACd;IACF;IACA,MAAMpB,eAAe,GAAGlD,QAAQ,CAACuE,MAAM,CAAC,CAACC,CAAC,EAAEvB,CAAC,KAAKA,CAAC,KAAKqB,KAAK,CAAC;IAC9DrE,WAAW,CAACiD,eAAe,CAAC;IAC5B9C,iBAAiB,CAAEqE,IAAI,IACrBA,IAAI,KAAKH,KAAK,GAAG,CAAC,GAAGG,IAAI,IAAIA,IAAI,GAAGH,KAAK,GAAG,CAAC,GAAG,CAAC,CACnD,CAAC;EACH,CAAC;EAED,MAAMI,iBAAiB,GAAIJ,KAAK,IAAK;IACnC1D,eAAe,CAAC0D,KAAK,CAAC;EACxB,CAAC;EAED,MAAMK,UAAU,GAAGA,CAAA,KAAM;IACvB/D,eAAe,CAAC,IAAI,CAAC;EACvB,CAAC;EAED,MAAMgE,mBAAmB,GAAGA,CAACN,KAAK,EAAEO,KAAK,EAAEC,KAAK,KAAK;IACnD,MAAM5B,eAAe,GAAG,CAAC,GAAGlD,QAAQ,CAAC;IACrC,MAAM8C,OAAO,GAAGI,eAAe,CAAC/C,cAAc,CAAC,CAACD,QAAQ,CAACoE,KAAK,CAAC;IAE/DxB,OAAO,CAAC+B,KAAK,CAAC,GAAGC,KAAK;IACtBhC,OAAO,CAACU,KAAK,GACXV,OAAO,CAACS,QAAQ,GAChBE,QAAQ,CAACX,OAAO,CAACY,KAAK,CAACC,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC,IAC7C,CAAC,GAAGb,OAAO,CAACc,QAAQ,GAAG,GAAG,CAAC;IAC9B3D,WAAW,CAACiD,eAAe,CAAC;EAC9B,CAAC;EACD,MAAM6B,UAAU,GAAIT,KAAK,IAAK;IAC5B1B,OAAO,CAACC,GAAG,CAACyB,KAAK,CAAC;IAClB,MAAMpB,eAAe,GAAG,CAAC,GAAGlD,QAAQ,CAAC;IACrC,IAAIgF,MAAM,GAAGhF,QAAQ,CAACG,cAAc,CAAC,CAACD,QAAQ;IAC9C8E,MAAM,GAAGA,MAAM,CAACT,MAAM,CAAC,CAACC,CAAC,EAAEvB,CAAC,KAAKA,CAAC,IAAIqB,KAAK,CAAC;IAC5CtE,QAAQ,CAACG,cAAc,CAAC,CAACD,QAAQ,GAAG8E,MAAM;IAC1C/E,WAAW,CAACiD,eAAe,CAAC;EAC9B,CAAC;EACD,MAAM+B,cAAc,GAAGA,CAAA,KAAM;IAC3B,OAAOjF,QAAQ,CAACG,cAAc,CAAC,CAACD,QAAQ,CAACgF,MAAM,CAC7C,CAAC1B,KAAK,EAAEV,OAAO,KAAKU,KAAK,GAAGV,OAAO,CAACU,KAAK,EACzC,CACF,CAAC;EACH,CAAC;EACD,MAAM2B,iBAAiB,GAAGA,CAAA,KAAM;IAC9B,MAAMjC,eAAe,GAAG,CAAC,GAAGlD,QAAQ,CAAC;IACrCkD,eAAe,CAAC/C,cAAc,CAAC,CAACD,QAAQ,GAAG,EAAE,CAAC,CAAC;IAC/CD,WAAW,CAACiD,eAAe,CAAC,CAAC,CAAC;EAChC,CAAC;EACD,MAAMkC,cAAc,GAClBpF,QAAQ,CAACG,cAAc,CAAC,CAACD,QAAQ,CAACgF,MAAM,CACtC,CAACG,GAAG,EAAEvC,OAAO,KAAKuC,GAAG,GAAGvC,OAAO,CAACU,KAAK,EACrC,CACF,CAAC,IACA,CAAC,GAAG/C,MAAM,GAAG,GAAG,CAAC;EAEpB,MAAM6E,QAAQ,GAAGF,cAAc,IAAI7E,GAAG,GAAG,GAAG,CAAC;EAC7C,MAAMiD,KAAK,GAAG+B,IAAI,CAACC,KAAK,CAACJ,cAAc,GAAGE,QAAQ,CAAC;EACnD,MAAMG,WAAW,GAAG,MAAAA,CAAA,KAAY;IAC9B,IAAI;MACF;MACA3E,SAAS,CAAC,IAAI,CAAC;MACfM,eAAe,CAAC,KAAK,CAAC;;MAEtB;MACAJ,SAAS,CAAC0E,OAAO,GAAG,MAAMC,SAAS,CAACC,YAAY,CAACC,YAAY,CAAC;QAC5DC,KAAK,EAAE;MACT,CAAC,CAAC;MACF,IAAI/E,QAAQ,CAAC2E,OAAO,EAAE;QACpB3E,QAAQ,CAAC2E,OAAO,CAACK,SAAS,GAAG/E,SAAS,CAAC0E,OAAO;MAChD;;MAEA;MACA,IAAIxG,MAAM,CAAC8G,WAAW,EAAE;QACtB9G,MAAM,CAAC+G,IAAI,CAAC,CAAC;MACf;;MAEA;MACA,IAAIlF,QAAQ,CAAC2E,OAAO,EAAE;QACpBxG,MAAM,CAACgH,IAAI,CACT;UACEC,WAAW,EAAE;YACXC,IAAI,EAAE,MAAM;YACZC,IAAI,EAAE,YAAY;YAClBC,MAAM,EAAEvF,QAAQ,CAAC2E,OAAO;YAAE;YAC1Ba,WAAW,EAAE;cACXC,UAAU,EAAE,aAAa,CAAE;YAC7B,CAAC;YACDC,kBAAkB,EAAE;UACtB,CAAC;UACDC,OAAO,EAAE;YACPC,OAAO,EAAE,CACP,iBAAiB,EACjB,YAAY,EACZ,YAAY,EACZ,gBAAgB,CACjB,CAAE;UACL;QACF,CAAC,EACD,UAAUC,GAAG,EAAE;UACb,IAAIA,GAAG,EAAE;YACPhE,OAAO,CAACiE,KAAK,CAAC,oBAAoB,EAAED,GAAG,CAAC;YACxCrH,MAAM,CACJ,CAAC,EACD,qDAAqD,EACrD,UACF,CAAC;YACD;UACF;UACAL,MAAM,CAAC8G,WAAW,GAAG,IAAI,CAAC,CAAC;UAC3B9G,MAAM,CAAC4H,KAAK,CAAC,CAAC;QAChB,CACF,CAAC;MACH;;MAEA;MACA5H,MAAM,CAAC6H,WAAW,CAAC,CAAC,CAAC,CAAC;MACtB7H,MAAM,CAAC8H,UAAU,CAAC,gBAAgBnD,MAAM,EAAE;QACxC,IAAI1C,YAAY,EAAE,OAAO,CAAC;QAC1BC,eAAe,CAAC,IAAI,CAAC,CAAC,CAAC;;QAEvB,MAAM4B,IAAI,GAAGa,MAAM,CAACoD,UAAU,CAACjE,IAAI;QACnCkE,UAAU,CAAC,CAAC,CAAC,CAAC;QACd,IAAI;UACF,MAAMnE,UAAU,CAACC,IAAI,CAAC,CAAC,CAAC;UACxB1C,cAAc,CAAC0C,IAAI,CAAC,CAAC,CAAC;QACxB,CAAC,CAAC,OAAO6D,KAAK,EAAE;UACdjE,OAAO,CAACiE,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;QAC9C,CAAC,SAAS;UACRzF,eAAe,CAAC,KAAK,CAAC;QACxB;MACF,CAAC,CAAC;IACJ,CAAC,CAAC,OAAOyF,KAAK,EAAE;MACdjE,OAAO,CAACiE,KAAK,CAAC,eAAe,EAAEA,KAAK,CAAC;MACrCtH,MAAM,CACJ,CAAC,EACD,qEAAqE,EACrE,UACF,CAAC;IACH;EACF,CAAC;EAED,MAAM2H,UAAU,GAAGA,CAAA,KAAM;IACvB,IAAI;MACF;MACA,IAAIlG,SAAS,CAAC0E,OAAO,EAAE;QACrB,MAAMyB,MAAM,GAAGnG,SAAS,CAAC0E,OAAO,CAAC0B,SAAS,CAAC,CAAC;QAC5C,IAAIC,KAAK,CAACC,OAAO,CAACH,MAAM,CAAC,EAAE;UACzBA,MAAM,CAAC7D,OAAO,CAAEiE,KAAK,IAAK;YACxB,IAAIA,KAAK,IAAI,OAAOA,KAAK,CAACtB,IAAI,KAAK,UAAU,EAAE;cAC7CsB,KAAK,CAACtB,IAAI,CAAC,CAAC,CAAC,CAAC;YAChB;UACF,CAAC,CAAC;QACJ;QACA;QACA,IAAIlF,QAAQ,CAAC2E,OAAO,EAAE;UACpB3E,QAAQ,CAAC2E,OAAO,CAACK,SAAS,GAAG,IAAI;QACnC;QACA/E,SAAS,CAAC0E,OAAO,GAAG,IAAI,CAAC,CAAC;MAC5B;;MAEA;MACA,IAAIxG,MAAM,IAAI,OAAOA,MAAM,CAAC+G,IAAI,KAAK,UAAU,EAAE;QAC/C/G,MAAM,CAAC+G,IAAI,CAAC,CAAC;QACb/G,MAAM,CAAC8G,WAAW,GAAG,KAAK,CAAC,CAAC;MAC9B;;MAEA;MACAlF,SAAS,CAAC,KAAK,CAAC;IAClB,CAAC,CAAC,OAAO+F,KAAK,EAAE;MACd/F,SAAS,CAAC,KAAK,CAAC;MAChB8B,OAAO,CAACiE,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;MAC9CtH,MAAM,CAAC,CAAC,EAAE,0CAA0C,EAAE,UAAU,CAAC;IACnE;EACF,CAAC;EAED,MAAMiI,MAAM,GAAGA,CAAA,KAAM;IACnB,IAAIhE,KAAK,GAAG,CAAC,EAAE;MACb5B,OAAO,CAAC,IAAI,CAAC;IACf;EACF,CAAC;EACD,MAAM6F,OAAO,GAAGA,CAAA,KAAM;IACpB7F,OAAO,CAAC,KAAK,CAAC;EAChB,CAAC;EACD,MAAM8F,cAAc,GAAGA,CAAA,KAAM;IAC3B5F,eAAe,CAAC,IAAI,CAAC;EACvB,CAAC;EACD,MAAM6F,eAAe,GAAGA,CAAA,KAAM;IAC5B7F,eAAe,CAAC,KAAK,CAAC;EACxB,CAAC;EACD,MAAM8F,cAAc,GAAGA,CAAA,KAAM;IAC3B5F,eAAe,CAAC,KAAK,CAAC;EACxB,CAAC;EACD,MAAM6F,cAAc,GAAGA,CAAA,KAAM;IAC3B7F,eAAe,CAAC,IAAI,CAAC;EACvB,CAAC;EACD,oBACEvC,OAAA,CAAAE,SAAA;IAAAmI,QAAA,GACG/F,YAAY,iBAAItC,OAAA,CAACR,OAAO;MAAC8I,OAAO,EAAEH;IAAe;MAAAI,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,EACpDtG,YAAY,iBACXpC,OAAA,CAACH,YAAY;MACXyI,OAAO,EAAEJ,eAAgB;MACzBS,QAAQ,EAAE7G,SAAU;MACpB0C,IAAI,EAAEA,IAAK;MACXoE,QAAQ,EAAE;IAAM;MAAAL,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACjB,CACF,EACAxG,IAAI,iBACHlC,OAAA,CAACJ,gBAAgB;MACfiJ,KAAK,EAAEb,OAAQ;MACfc,WAAW,EAAE/E,KAAM;MACnBtD,QAAQ,EAAEF,QAAQ,CAACG,cAAc,CAAC,CAACD,QAAS;MAC5CqB,SAAS,EAAEA,SAAU;MACrBqC,QAAQ,EAAEnD,MAAO;MACjB+H,GAAG,EAAEjI;IAAI;MAAAyH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CACF,EACAtH,MAAM,iBACLpB,OAAA;MAAKgJ,SAAS,EAAC,aAAa;MAAAX,QAAA,gBAC1BrI,OAAA;QAAOiJ,GAAG,EAAE3H,QAAS;QAAC4H,QAAQ;QAACC,KAAK,EAAC,OAAO;QAACC,MAAM,EAAC;MAAO;QAAAb,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAE9D1I,OAAA;QACEgJ,SAAS,EAAC,iCAAiC;QAC3CK,OAAO,EAAE5B,UAAW;QACpB6B,KAAK,EAAE;UAAEC,eAAe,EAAE,KAAK;UAAEC,KAAK,EAAE;QAAQ,CAAE;QAAAnB,QAAA,EACnD;MAED;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CACN,eACD1I,OAAA;MAAKgJ,SAAS,EAAC,mBAAmB;MAAAX,QAAA,gBAChCrI,OAAA;QAAKgJ,SAAS,EAAC,aAAa;QAAAX,QAAA,GACzB9H,QAAQ,CAACkJ,GAAG,CAAC,CAAC1E,CAAC,EAAEF,KAAK,kBACrB7E,OAAA;UAAiBgJ,SAAS,EAAC,aAAa;UAAAX,QAAA,gBACtCrI,OAAA;YACEgJ,SAAS,EACPnE,KAAK,KAAKnE,cAAc,GACpB,oBAAoB,GACpB,aACL;YACD2I,OAAO,EAAEA,CAAA,KAAM;cACb1I,iBAAiB,CAACkE,KAAK,CAAC;cACxB1D,eAAe,CAAC,IAAI,CAAC;YACvB,CAAE;YAAAkH,QAAA,GACH,uBACS,EAACxD,KAAK,GAAG,CAAC;UAAA;YAAA0D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACZ,CAAC,eACT1I,OAAA;YACEgJ,SAAS,EAAC,aAAa;YACvBK,OAAO,EAAEA,CAAA,KAAMzE,aAAa,CAACC,KAAK,CAAE;YAAAwD,QAAA,EACrC;UAED;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA,GAnBD7D,KAAK;UAAA0D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAoBV,CACN,CAAC,eACF1I,OAAA;UAAQqJ,OAAO,EAAE3E,UAAW;UAACsE,SAAS,EAAC,aAAa;UAAAX,QAAA,EAAC;QAErD;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eACN1I,OAAA;QAAKgJ,SAAS,EAAC,SAAS;QAAAX,QAAA,gBACtBrI,OAAA;UAAKgJ,SAAS,EAAC,iBAAiB;UAAAX,QAAA,gBAC9BrI,OAAA;YAAOgJ,SAAS,EAAC,YAAY;YAAAX,QAAA,EAAC;UAAkB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACxD1I,OAAA;YACEgJ,SAAS,EAAC,YAAY;YACtBpC,IAAI,EAAC,MAAM;YACXvB,KAAK,EAAEzE,WAAY;YACnB8I,QAAQ,EAAGC,CAAC,IAAK;cACf9I,cAAc,CAAC8I,CAAC,CAAC9C,MAAM,CAACxB,KAAK,CAAC;cAC9B,IAAIsE,CAAC,CAAC9C,MAAM,CAACxB,KAAK,IAAI,EAAE,EAAE;gBACxB,MAAMZ,CAAC,GAAG7C,IAAI,CAACkD,MAAM,CAAC,CAACzB,OAAO,EAAEwB,KAAK,KACnCxB,OAAO,CAACO,GAAG,CAACgG,QAAQ,CAACD,CAAC,CAAC9C,MAAM,CAACxB,KAAK,CACrC,CAAC;gBACDpD,aAAa,CAACwC,CAAC,CAAC;cAClB,CAAC,MAAM;gBACLxC,aAAa,CAAC,EAAE,CAAC;cACnB;YACF;UAAE;YAAAsG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACF1I,OAAA;YAAI6J,EAAE,EAAC,kBAAkB;YAAAxB,QAAA,EACtBrG,UAAU,CAACyH,GAAG,CAAC,CAACpG,OAAO,EAAEwB,KAAK,KAAK;cAClC,oBACE7E,OAAA;gBAEEqJ,OAAO,EAAEA,CAAA,KAAM;kBACbxI,cAAc,CAACwC,OAAO,CAACO,GAAG,CAAC;kBAC3B3B,aAAa,CAAC,EAAE,CAAC;gBACnB,CAAE;gBAAAoG,QAAA,EAEDhF,OAAO,CAACO;cAAG,GANPiB,KAAK;gBAAA0D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAOR,CAAC;YAET,CAAC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC,eACL1I,OAAA;YACEsJ,KAAK,EAAE;cAAEQ,SAAS,EAAE,MAAM;cAAEN,KAAK,EAAE;YAAQ,CAAE;YAC7CH,OAAO,EAAErD,WAAY;YACrBgD,SAAS,EAAC,aAAa;YAAAX,QAAA,EACxB;UAED;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACT1I,OAAA;YACEqJ,OAAO,EAAEA,CAAA,KAAM/F,UAAU,CAAC,CAAE;YAC5BgG,KAAK,EAAE;cAAEE,KAAK,EAAE,OAAO;cAAEO,UAAU,EAAE;YAAO,CAAE;YAC9Cf,SAAS,EAAC,aAAa;YAAAX,QAAA,EACxB;UAED;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eACN1I,OAAA;UAAKgJ,SAAS,EAAC,IAAI;UAAAX,QAAA,gBACjBrI,OAAA;YAAQgJ,SAAS,EAAC,SAAS;YAACK,OAAO,EAAEjB,cAAe;YAAAC,QAAA,EAAC;UAErD;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACT1I,OAAA;YAAAuI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACN1I,OAAA;YAAQgJ,SAAS,EAAC,aAAa;YAACK,OAAO,EAAEpB,cAAe;YAAAI,QAAA,EAAC;UAEzD;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACN1I,OAAA;QAAKgJ,SAAS,EAAC,cAAc;QAAAX,QAAA,gBAC3BrI,OAAA;UAAAqI,QAAA,EAAI;QAAkB;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC3B1I,OAAA;UAAAqI,QAAA,gBACErI,OAAA;YAAAqI,QAAA,eACErI,OAAA;cAAAqI,QAAA,gBACErI,OAAA;gBAAAqI,QAAA,EAAI;cAAO;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAChB1I,OAAA;gBAAAqI,QAAA,EAAI;cAAY;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACrB1I,OAAA;gBAAAqI,QAAA,EAAI;cAAQ;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACjB1I,OAAA;gBAAAqI,QAAA,EAAI;cAAO;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAChB1I,OAAA;gBAAAqI,QAAA,EAAI;cAAY;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACrB1I,OAAA;gBAAAqI,QAAA,EAAI;cAAU;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC,eACR1I,OAAA;YAAAqI,QAAA,GACG9H,QAAQ,CAACG,cAAc,CAAC,CAACD,QAAQ,CAACgJ,GAAG,CAAC,CAACpG,OAAO,EAAEwB,KAAK,kBACpD7E,OAAA;cAAgBgK,aAAa,EAAEA,CAAA,KAAM/E,iBAAiB,CAACJ,KAAK,CAAE;cAAAwD,QAAA,gBAC5DrI,OAAA;gBAAAqI,QAAA,EAAKhF,OAAO,CAACO;cAAG;gBAAA2E,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACtB1I,OAAA;gBAAAqI,QAAA,EAAKhF,OAAO,CAACsD;cAAI;gBAAA4B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACvB1I,OAAA;gBAAAqI,QAAA,EACGnH,YAAY,KAAK2D,KAAK,gBACrB7E,OAAA;kBACEgJ,SAAS,EAAC,YAAY;kBACtBpC,IAAI,EAAC,QAAQ;kBACbvB,KAAK,EAAEhC,OAAO,CAACS,QAAS;kBACxB4F,QAAQ,EAAGC,CAAC,IACVxE,mBAAmB,CACjBN,KAAK,EACL,UAAU,EACVoF,MAAM,CAACN,CAAC,CAAC9C,MAAM,CAACxB,KAAK,CACvB,CACD;kBACD6E,MAAM,EAAEhF;gBAAW;kBAAAqD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpB,CAAC,GAEFrF,OAAO,CAACS;cACT;gBAAAyE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACL1I,OAAA;gBAAAqI,QAAA,EAAKhF,OAAO,CAACY;cAAK;gBAAAsE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACxB1I,OAAA;gBAAAqI,QAAA,EACGnH,YAAY,KAAK2D,KAAK,gBACrB7E,OAAA;kBACEgJ,SAAS,EAAC,YAAY;kBACtBpC,IAAI,EAAC,QAAQ;kBACbvB,KAAK,EAAEhC,OAAO,CAACc,QAAS;kBACxBuF,QAAQ,EAAGC,CAAC,IACVxE,mBAAmB,CACjBN,KAAK,EACL,UAAU,EACVoF,MAAM,CAACN,CAAC,CAAC9C,MAAM,CAACxB,KAAK,CACvB,CACD;kBACD6E,MAAM,EAAEhF;gBAAW;kBAAAqD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpB,CAAC,GAEFrF,OAAO,CAACc;cACT;gBAAAoE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACL1I,OAAA;gBAAAqI,QAAA,EAAKhF,OAAO,CAACU,KAAK,CAACoG,cAAc,CAAC,OAAO;cAAC;gBAAA5B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAChD1I,OAAA;gBACEgJ,SAAS,EAAC,YAAY;gBACtBK,OAAO,EAAEA,CAAA,KAAM;kBACb/D,UAAU,CAACT,KAAK,CAAC;gBACnB,CAAE;gBAAAwD,QAAA,EACH;cAED;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA,GAlDE7D,KAAK;cAAA0D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAmDV,CACL,CAAC,eACF1I,OAAA;cAAAqI,QAAA,gBACErI,OAAA;gBAAIoK,OAAO,EAAE,CAAE;gBAACd,KAAK,EAAE;kBAAEe,SAAS,EAAE;gBAAQ,CAAE;gBAAAhC,QAAA,eAC5CrI,OAAA;kBAAAqI,QAAA,EAAQ;gBAAU;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzB,CAAC,eACL1I,OAAA;gBAAIsJ,KAAK,EAAE;kBAAEe,SAAS,EAAE;gBAAQ,CAAE;gBAAAhC,QAAA,EAC/B7C,cAAc,CAAC,CAAC,CAAC2E,cAAc,CAAC,OAAO;cAAC;gBAAA5B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvC,CAAC,eACL1I,OAAA;gBAAIgJ,SAAS,EAAC,YAAY;gBAACK,OAAO,EAAE3D,iBAAkB;gBAAA2C,QAAA,EAAC;cAEvD;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eACN1I,OAAA;QAAKgJ,SAAS,EAAC,SAAS;QAAAX,QAAA,gBACtBrI,OAAA;UAAKgJ,SAAS,EAAC,iBAAiB;UAAAX,QAAA,gBAC9BrI,OAAA;YAAOgJ,SAAS,EAAC,YAAY;YAAAX,QAAA,EAAC;UAAe;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACrD1I,OAAA;YACEgJ,SAAS,EAAC,YAAY;YACtBpC,IAAI,EAAC,QAAQ;YACbvB,KAAK,EAAEvE,GAAI;YACX4I,QAAQ,EAAGC,CAAC,IAAK5I,MAAM,CAACkJ,MAAM,CAACN,CAAC,CAAC9C,MAAM,CAACxB,KAAK,CAAC;UAAE;YAAAkD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjD,CAAC,eACF1I,OAAA;YAAOgJ,SAAS,EAAC,YAAY;YAACM,KAAK,EAAE;cAAEQ,SAAS,EAAE;YAAO,CAAE;YAAAzB,QAAA,GAAC,8DACxB,EAAC,GAAG;UAAA;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjC,CAAC,eACR1I,OAAA;YACEgJ,SAAS,EAAC,YAAY;YACtBpC,IAAI,EAAC,QAAQ;YACbvB,KAAK,EAAErE,MAAO;YACd0I,QAAQ,EAAGC,CAAC,IAAK1I,SAAS,CAACgJ,MAAM,CAACN,CAAC,CAAC9C,MAAM,CAACxB,KAAK,CAAC;UAAE;YAAAkD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eACN1I,OAAA;UAAKgJ,SAAS,EAAC,QAAQ;UAAAX,QAAA,eACrBrI,OAAA;YAAIsJ,KAAK,EAAE;cAAEQ,SAAS,EAAE;YAAO,CAAE;YAAAzB,QAAA,GAAC,kCAClB,EAACtE,KAAK,CAACoG,cAAc,CAAC,OAAO,CAAC;UAAA;YAAA5B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1C;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eACN1I,OAAA;UACEgJ,SAAS,EAAC,aAAa;UACvBM,KAAK,EAAE;YAAEE,KAAK,EAAE,OAAO;YAAEM,SAAS,EAAE;UAAO,CAAE;UAC7CT,OAAO,EAAEtB,MAAO;UAAAM,QAAA,EACjB;QAED;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA,eACN,CAAC;AAEP,CAAC;AAACtI,EAAA,CAjiBID,OAAO;EAAA,QAC2BT,UAAU,EAUtBC,OAAO;AAAA;AAAA2K,EAAA,GAX7BnK,OAAO;AAmiBb,eAAeA,OAAO;AAAC,IAAAmK,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}